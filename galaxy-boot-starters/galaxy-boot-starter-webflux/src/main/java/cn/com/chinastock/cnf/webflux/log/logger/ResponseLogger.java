package cn.com.chinastock.cnf.webflux.log.logger;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import com.alibaba.fastjson2.JSON;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.alibaba.fastjson2.JSONPath.extract;

/**
 * ResponseLogger 类用于记录WebFlux环境下HTTP响应的日志信息。
 * 该类提供了多种记录响应日志的方法，支持普通日志记录和敏感字段掩码处理。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>记录HTTP响应的基本信息（状态码、响应头等）</li>
 *     <li>记录响应体内容</li>
 *     <li>支持敏感字段掩码处理</li>
 *     <li>自动提取业务响应中的Meta代码</li>
 *     <li>支持可选的响应头记录</li>
 * </ul>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #log(ServerHttpResponse, boolean, String)}：记录响应日志，包含响应体字符串。</li>
 *     <li>{@link #logSensitive(ServerHttpResponse, boolean, Object)}：记录响应日志，对敏感字段进行掩码处理。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class ResponseLogger {
    private final static IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(ResponseLogger.class);

    /**
     * 记录响应日志，包含响应体字符串
     * 
     * @param response HTTP响应对象
     * @param isLogHeaders 是否记录响应头信息
     * @param body 响应体内容字符串
     */
    public static void log(ServerHttpResponse response, boolean isLogHeaders, String body) {
        Map<String, Object> detail = createStandardParams(response, isLogHeaders);
        if (Objects.nonNull(body)) {
            detail.put("body", body);

            try {
                MediaType contentType = response.getHeaders().getContentType();
                if (!body.isEmpty() && contentType != null && contentType.isCompatibleWith(MediaType.APPLICATION_JSON)) {
                    extractMetaCode(body).ifPresent(metaCode -> detail.put("meta_code", metaCode));
                }
            } catch (Exception e) {
            }
        }
        logger.info(LogCategory.RESPONSE_LOG, HttpLogUtils.formatLogInfo(detail));
    }

    /**
     * 记录响应日志，对敏感字段进行掩码处理
     * 
     * @param response HTTP响应对象
     * @param isLogHeaders 是否记录响应头信息
     * @param body 响应体对象
     */
    public static void logSensitive(ServerHttpResponse response, boolean isLogHeaders, Object body) {
        Map<String, Object> detail = createStandardParams(response, isLogHeaders);
        if (Objects.nonNull(body)) {
            detail.put("body", HttpLogUtils.maskSensitiveFields(body));

            if (body instanceof BaseResponse<?> baseResponse) {
                detail.put("meta_code", baseResponse.getMeta().getCode());
            }
        } else {
            detail.put("body", null);
            detail.put("meta_code", null);
        }

        logger.info(LogCategory.RESPONSE_LOG, HttpLogUtils.formatLogInfo(detail));
    }

    /**
     * 创建标准的响应参数信息
     * 
     * @param response HTTP响应对象
     * @param isLogHeaders 是否记录响应头信息
     * @return 包含响应基本信息的Map
     */
    private static Map<String, Object> createStandardParams(ServerHttpResponse response, boolean isLogHeaders) {
        Map<String, Object> detail = new HashMap<>();

        try {
            if (response.getStatusCode() != null) {
                detail.put("http_status_code", response.getStatusCode().value());
            } else {
                detail.put("http_status_code", null);
            }
        } catch (Exception e) {
            detail.put("http_status_code", null);
        }

        if (isLogHeaders) {
            try {
                detail.put("headers", JSON.toJSONString(response.getHeaders().toSingleValueMap()));
            } catch (Exception e) {
                detail.put("headers", "{}"); // 默认空headers
            }
        }
        return detail;
    }

    /**
     * 从响应体字符串中提取Meta代码
     * 
     * @param body 响应体字符串
     * @return 包含Meta代码的Optional对象
     */
    private static Optional<String> extractMetaCode(String body) {
        try {
            Object metaCode = extract(body, "$.meta.code");
            return Optional.ofNullable(metaCode).map(Object::toString);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
