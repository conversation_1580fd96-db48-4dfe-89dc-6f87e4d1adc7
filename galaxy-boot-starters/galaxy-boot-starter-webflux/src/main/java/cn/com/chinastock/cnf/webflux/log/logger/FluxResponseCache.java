package cn.com.chinastock.cnf.webflux.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * FluxResponseCache 类用于在WebFlux环境下缓存HTTP响应数据。
 * 该类通过装饰器模式包装ServerHttpResponse，在响应体数据流过时进行缓存，
 * 以便后续的日志记录操作能够访问到响应体内容。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>根据Content-Type智能判断是否需要缓存响应体</li>
 *     <li>使用装饰器模式非阻塞地缓存响应体数据</li>
 *     <li>支持流式数据处理，避免内存溢出</li>
 *     <li>提供日志记录功能，支持响应头和响应体的记录</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * {@code
 * FluxResponseCache responseCache = new FluxResponseCache(response);
 * ServerHttpResponseDecorator decorator = responseCache.responseDecorator();
 * // 在响应处理完成后记录日志
 * responseCache.log(true);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @see DataBufferCache
 * @see ResponseLogger
 * @see ServerHttpResponseDecorator
 */
public class FluxResponseCache {

    private final ServerHttpResponse response;
    private final DataBufferCache dataBufferCache;
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    /**
     * 构造函数，初始化响应缓存对象
     *
     * @param response HTTP响应对象，不能为null
     */
    public FluxResponseCache(ServerHttpResponse response) {
        this.response = response;
        this.dataBufferCache = DataBufferCache.createNullableBy(response.getHeaders().getContentType());
    }

    /**
     * 创建响应装饰器，用于拦截和缓存响应体数据
     * 
     * <p>该方法根据Content-Type判断是否需要缓存响应体：</p>
     * <ul>
     *     <li>如果Content-Type不支持缓存，返回原始响应装饰器</li>
     *     <li>如果Content-Type支持缓存，返回能够缓存响应体的装饰器</li>
     * </ul>
     * 
     * @return ServerHttpResponseDecorator 响应装饰器对象
     */
    public ServerHttpResponseDecorator responseDecorator() {
        if (Objects.isNull(dataBufferCache)) {
            return new ServerHttpResponseDecorator(response);
        }

        return new ServerHttpResponseDecorator(response) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                return super.writeWith(Flux.from(body)
                        .doOnNext(buffer -> {
                            dataBufferCache.cache(buffer);
                        }));
            }
        };
    }

    /**
     * 记录响应日志
     *
     * <p>该方法根据是否有缓存的响应体数据来决定日志记录方式：</p>
     * <ul>
     *     <li>如果有缓存数据，记录包含响应体的完整日志</li>
     *     <li>如果没有缓存数据，仅记录响应基本信息</li>
     * </ul>
     *
     * @param responseHeadersEnabled 是否启用响应头记录
     */
    public void log(boolean responseHeadersEnabled) {
        try {
            String responseBody = null;
            if (Objects.nonNull(dataBufferCache)) {
                responseBody = dataBufferCache.outputToString();
            }
            // 无论是否有响应体，都要记录响应日志
            ResponseLogger.log(response, responseHeadersEnabled, responseBody);
        } catch (Exception e) {
            logger.warn("Failed to log response: " + e.getMessage());
        }
    }

}
