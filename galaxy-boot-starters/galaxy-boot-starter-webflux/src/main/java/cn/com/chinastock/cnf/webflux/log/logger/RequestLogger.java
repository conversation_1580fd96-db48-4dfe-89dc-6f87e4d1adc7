package cn.com.chinastock.cnf.webflux.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import com.alibaba.fastjson2.JSON;
import org.springframework.http.server.reactive.ServerHttpRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * RequestLogger 类用于记录WebFlux环境下HTTP请求的日志信息。
 * 该类提供了多种记录请求日志的方法，支持普通日志记录和敏感字段掩码处理。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>记录HTTP请求的基本信息（查询参数、请求头等）</li>
 *     <li>记录请求体内容</li>
 *     <li>支持敏感字段掩码处理</li>
 *     <li>支持可选的请求头记录</li>
 * </ul>
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #log(ServerHttpRequest, boolean)}：记录请求日志，不包含请求体。</li>
 *     <li>{@link #log(ServerHttpRequest, boolean, String)}：记录请求日志，包含请求体。</li>
 *     <li>{@link #logSensitive(ServerHttpRequest, boolean, Object)}：记录请求日志，对敏感字段进行掩码处理。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class RequestLogger {
    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(RequestLogger.class);

    /**
     * 记录请求日志，不包含请求体
     * 
     * @param request HTTP请求对象
     * @param isLogHeaders 是否记录请求头信息
     */
    public static void log(ServerHttpRequest request, boolean isLogHeaders) {
        log(request, isLogHeaders, null);
    }

    /**
     * 记录请求日志，包含请求体
     * 
     * @param request HTTP请求对象
     * @param isLogHeaders 是否记录请求头信息
     * @param body 请求体内容字符串
     */
    public static void log(ServerHttpRequest request, boolean isLogHeaders, String body) {
        Map detail = createStandardParams(request, isLogHeaders);

        if (Objects.nonNull(body)) {
            detail.put("body", HttpLogUtils.flattenJson(body));
        }

        logger.info(LogCategory.REQUEST_LOG, HttpLogUtils.formatLogInfo(detail));
    }

    /**
     * 记录请求日志，对敏感字段进行掩码处理
     * 
     * @param request HTTP请求对象
     * @param isLogHeaders 是否记录请求头信息
     * @param body 请求体对象
     */
    public static void logSensitive(ServerHttpRequest request, boolean isLogHeaders, Object body) {
        Map<String, Object> detail = createStandardParams(request, isLogHeaders);

        if (Objects.nonNull(body)) {
            detail.put("body", HttpLogUtils.maskSensitiveFields(body));
        } else {
            detail.put("body", null);  // 显式添加body=null
        }

        logger.info(LogCategory.REQUEST_LOG, HttpLogUtils.formatLogInfo(detail));
    }

    /**
     * 创建标准的请求参数信息
     * 
     * @param request HTTP请求对象
     * @param isLogHeaders 是否记录请求头信息
     * @return 包含请求基本信息的Map
     */
    private static Map createStandardParams(ServerHttpRequest request, boolean isLogHeaders) {
        Map detail = new HashMap<>();
        detail.put("query_string", JSON.toJSONString(request.getQueryParams()));
        if (isLogHeaders) {
            detail.put("headers", JSON.toJSONString(request.getHeaders().toSingleValueMap()));
        }
        return detail;
    }
}
