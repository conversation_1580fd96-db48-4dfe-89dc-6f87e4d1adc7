# SpanId 一致性问题修复

## 问题描述

在启用 `controller-log-enabled=true` 模式时，发现 PerformanceLog 的 spanId 与 RequestLog、BusinessLog 的 spanId 不一致。

### 问题日志示例

```
V1|2025-08-29T18:01:01.230+0800|162684831986041|INFO|http-nio-8088-exec-1|*************|POST|/api/test/log/request-body|3dc103bfcd2b6890b2fe383600dc778e|-|4922506ffc1da970|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.RequestLogger|REQUEST_LOG|headers={"content-length":"34","host":"localhost:8088","content-type":"application/json","accept-encoding":"br, deflate, gzip, x-gzip","user-agent":"IntelliJ HTTP Client/IntelliJ IDEA 2024.3.6","accept":"*/*"} body={"data":"","maskData":"***"} query_string=null |-

V1|2025-08-29T18:01:01.230+0800|162684832105750|INFO|http-nio-8088-exec-1|*************|POST|/api/test/log/request-body|3dc103bfcd2b6890b2fe383600dc778e|-|4922506ffc1da970|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.examples.controller.LogController|BUSINESS_LOG|请求体: LogDTO{data=''}|-

V1|2025-08-29T18:01:01.234+0800|162684835433916|INFO|http-nio-8088-exec-1|*************|POST|/api/test/log/request-body|3dc103bfcd2b6890b2fe383600dc778e|-|4922506ffc1da970|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.ResponseLogger|RESPONSE_LOG|headers={} http_status_code=200 meta_code=0 body={"data":{"data":"","maskData":"***"},"meta":{"code":"0","success":true,"message":"成功"}} |-

V1|2025-08-29T18:01:01.249+0800|162684850368166|INFO|http-nio-8088-exec-1|*************|POST|/api/test/log/request-body|3dc103bfcd2b6890b2fe383600dc778e|-|331dd6999e2f7428|EXP|galaxy-boot-core-example-service|cn.com.chinastock.cnf.core.log.logger.PerformanceLogger|PERFORMANCE_LOG|cost=94 unit=ms|-
```

**问题**：前三条日志的 spanId 是 `4922506ffc1da970`，但最后的 PerformanceLog 的 spanId 变成了 `331dd6999e2f7428`。

## 根本原因分析

### 1. 架构问题

在 `controller-log-enabled=true` 模式下：
- **RequestLog 和 ResponseLog** 由 `ControllerLogAspect` 处理
- **PerformanceLog** 仍然由 `LogFilter` 处理
- `ControllerLogAspect` 中缺少性能日志记录逻辑

### 2. 执行时序问题

1. `LogFilter.loadLogContext()` - 初始化 traceId 和 spanId
2. `ControllerLogAspect.around()` - 记录请求日志（使用初始 spanId）
3. 业务方法执行 - 记录业务日志（使用初始 spanId）
4. `ControllerLogAspect.beforeBodyWrite()` - 记录响应日志（使用初始 spanId）
5. `LogFilter.finally` - 调用 `LogContext.clear()` 清理上下文
6. `LogFilter.safeStopPerformanceWatch()` - 记录性能日志（此时可能创建新的 spanId）

### 3. MicrometerIntegratedTraceContext 的问题

当记录性能日志时，如果之前的 span 已经结束或不可用，`MicrometerIntegratedTraceContext` 会创建一个新的 span：

```java
private TraceContext loadCurrentTraceContext(Tracer currentTracer) {
    Span activeSpan = currentTracer.currentSpan();
    if (activeSpan != null) {
        // 使用现有 span
        return activeSpan.context();
    } else {
        // 创建新的 span - 这里导致 spanId 变化！
        currentSpan = currentTracer.nextSpan().name("http-request").start();
        return currentSpan.context();
    }
}
```

## 解决方案

### 方案1：在 ControllerLogAspect 中添加性能日志记录（已实现）

修改 `ControllerLogAspect.around()` 方法，添加性能监控：

```java
@Around("@within(org.springframework.web.bind.annotation.RestController)")
public Object around(ProceedingJoinPoint point) throws Throwable {
    if (logProperties.isControllerLogEnabled()) {
        // 记录请求信息
        frameworkLogger.logRequestWithFieldsMasked(getRequestObject(point));
        // 启动性能监控
        frameworkLogger.startPerformanceWatch();
    }

    try {
        return point.proceed();
    } finally {
        if (logProperties.isControllerLogEnabled()) {
            // 停止性能监控并记录耗时
            frameworkLogger.stopPerformanceWatch();
        }
    }
}
```

### 方案2：修改 LogFilter 避免重复记录性能日志（已实现）

在 `LogFilter` 中检查是否启用了 controller log 模式，避免重复记录：

```java
private void writeResponseLogAndStopWatch(ContentCachingResponseWrapper responseWrapper) {
    try {
        frameworkLogger.logResponse(responseWrapper);
        responseWrapper.copyBodyToResponse();
    } catch (Exception e) {
        GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeResponseLogAndStopWatch: response logging failed", e);
    } finally {
        // 在 controller log enabled 模式下，性能日志由 ControllerLogAspect 处理
        if (!logProperties.isControllerLogEnabled()) {
            safeStopPerformanceWatch();
        }
        LogContext.clear();
    }
}
```

### 方案3：改进 MicrometerIntegratedTraceContext（已实现）

优化 `loadMicrometerTraceContext()` 方法，优先保持已有的 spanId：

```java
private boolean loadMicrometerTraceContext() {
    Tracer currentTracer = getCurrentTracer();
    if (currentTracer != null) {
        try {
            // 如果 fallbackContext 已经有有效的 traceId 和 spanId，优先保持一致性
            if (StringUtils.hasText(fallbackContext.getTraceId()) && 
                StringUtils.hasText(fallbackContext.getSpanId())) {
                return true;
            }
            
            TraceContext currentTraceContext = loadCurrentTraceContext(currentTracer);
            if (currentTraceContext != null) {
                fallbackContext.setTraceId(currentTraceContext.traceId());
                fallbackContext.setSpanId(currentTraceContext.spanId());
                fallbackContext.setParentSpanId(currentTraceContext.parentId());
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    return false;
}
```

## 修复效果

修复后，所有日志（RequestLog、BusinessLog、ResponseLog、PerformanceLog）将使用相同的 spanId，确保链路追踪的一致性。

## 测试验证

创建了 `ControllerLogAspectSpanIdConsistencyTest` 测试类来验证修复效果，确保：
1. 所有日志的 spanId 保持一致
2. 所有日志的 traceId 保持一致
3. 性能日志能够正常记录

## 影响范围

- **galaxy-boot-starter-web**: 修改了 `ControllerLogAspect` 和 `LogFilter`
- **galaxy-boot-core**: 修改了 `MicrometerIntegratedTraceContext`
- **向后兼容**: 所有修改都是向后兼容的，不会影响现有功能
