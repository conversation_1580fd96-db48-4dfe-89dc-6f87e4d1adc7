package cn.com.chinastock.cnf.core.log.aspect;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.context.MicrometerIntegratedTraceContext;
import cn.com.chinastock.cnf.core.log.test.TestAppender;
import io.micrometer.tracing.Tracer;
import org.apache.logging.log4j.core.LogEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

/**
 * 测试 ControllerLogAspect 修复后 spanId 的一致性
 */
@SpringBootTest(classes = {ControllerLogAspectSpanIdConsistencyTest.TestController.class})
@TestPropertySource(properties = {
        "galaxy.log.controller-log-enabled=true",
        "galaxy.log.performance-log-enabled=true"
})
public class ControllerLogAspectSpanIdConsistencyTest {

    @MockBean
    private Tracer tracer;

    private TestAppender appender;
    private ITraceContext traceContext;
    private LogProperties logProperties;

    @BeforeEach
    void setUp() {
        appender = new TestAppender();
        appender.start();

        logProperties = new LogProperties();
        logProperties.setControllerLogEnabled(true);
        logProperties.setPerformanceLogEnabled(true);

        traceContext = new MicrometerIntegratedTraceContext(mock(Tracer.class));
        
        // 模拟请求上下文
        Map<String, String> headers = new HashMap<>();
        LogContext.current().loadContext(logProperties, traceContext, "POST", "/api/test/log/request-body", headers);
    }

    @Test
    void shouldMaintainConsistentSpanIdAcrossAllLogs() {
        // Given
        TestController controller = new TestController();
        TestRequestDto request = new TestRequestDto("test data");

        // When - 模拟请求处理过程
        // 1. 记录请求日志
        GalaxyLogger.info(LogCategory.REQUEST_LOG, "headers={} body={} query_string=null", 
                "{\"content-type\":\"application/json\"}", 
                "{\"data\":\"test data\",\"maskData\":\"***\"}");

        // 2. 记录业务日志
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "请求体: {}", request);

        // 3. 记录响应日志
        GalaxyLogger.info(LogCategory.RESPONSE_LOG, "headers={} http_status_code=200 meta_code=0 body={}", 
                "{}", 
                "{\"data\":{\"data\":\"test data\",\"maskData\":\"***\"},\"meta\":{\"code\":\"0\",\"success\":true,\"message\":\"成功\"}}");

        // 4. 记录性能日志
        GalaxyLogger.info(LogCategory.PERFORMANCE_LOG, "cost=94 unit=ms");

        // Then - 验证所有日志的 spanId 一致
        List<LogEvent> events = appender.getEvents();
        assertThat(events).hasSize(4);

        String expectedSpanId = events.get(0).getContextData().getValue(SPAN_ID);
        assertThat(expectedSpanId).isNotNull().matches("[0-9a-f]{16}");

        // 验证所有日志的 spanId 都相同
        for (LogEvent event : events) {
            String actualSpanId = event.getContextData().getValue(SPAN_ID);
            assertThat(actualSpanId)
                    .as("SpanId should be consistent across all logs")
                    .isEqualTo(expectedSpanId);
        }

        // 验证 traceId 也保持一致
        String expectedTraceId = events.get(0).getContextData().getValue(TRACE_ID);
        assertThat(expectedTraceId).isNotNull().matches("[0-9a-f]{32}");

        for (LogEvent event : events) {
            String actualTraceId = event.getContextData().getValue(TRACE_ID);
            assertThat(actualTraceId)
                    .as("TraceId should be consistent across all logs")
                    .isEqualTo(expectedTraceId);
        }
    }

    @RestController
    static class TestController {
        @PostMapping("/api/test/log/request-body")
        public TestResponseDto requestBody(@RequestBody TestRequestDto request) {
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "请求体: {}", request);
            return new TestResponseDto(request.getData(), "***");
        }
    }

    static class TestRequestDto {
        private String data;

        public TestRequestDto(String data) {
            this.data = data;
        }

        public String getData() {
            return data;
        }

        @Override
        public String toString() {
            return "TestRequestDto{data='" + data + "'}";
        }
    }

    static class TestResponseDto {
        private String data;
        private String maskData;

        public TestResponseDto(String data, String maskData) {
            this.data = data;
            this.maskData = maskData;
        }

        public String getData() {
            return data;
        }

        public String getMaskData() {
            return maskData;
        }
    }
}
