package cn.com.chinastock.cnf.mdatasource.properties;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;

import java.util.Map;
import java.util.HashMap;

/**
 * DataSourceDefinition 类定义单个数据源的所有配置信息。
 * 该类包含了标准的Spring Boot数据源配置以及包路径配置，
 * 用于在多数据源环境中配置每个独立的数据源。
 *
 * JPA、MyBatis和MyBatisPlus的配置通过动态属性映射来处理，避免在类加载时强制依赖这些框架。
 *
 * <AUTHOR>
 */
public class DataSourceDefinition {

    private boolean primary = false;

    /**
     * 数据源类型，支持 jpa、mybatis 和 mybatis-plus
     */
    private DataSourceType type = DataSourceType.JPA;

    /**
     * 对应 `spring.datasource:` 标准的 Spring Boot 数据源属性
     */
    private DataSourceProperties datasource = new DataSourceProperties();



    /**
     * Hikari连接池配置属性
     */
    private HikariProperties hikari = new HikariProperties();

    private ScopePackagesProperties packages = new ScopePackagesProperties();

    /**
     * 动态配置属性映射，用于存储MyBatis和MyBatisPlus等框架的配置
     * 这样可以避免在类加载时强制依赖这些框架的配置类
     */
    private Map<String, Object> dynamicProperties = new HashMap<>();

    /**
     * 获取数据源配置属性
     * 
     * @return 数据源配置属性对象
     */
    public DataSourceProperties getDatasource() {
        return datasource;
    }

    /**
     * 设置数据源配置属性
     * 
     * @param datasource 数据源配置属性对象
     */
    public void setDatasource(DataSourceProperties datasource) {
        this.datasource = datasource;
    }

    /**
     * 设置JPA配置属性（通过动态属性映射）
     *
     * @param jpa JPA配置属性映射
     */
    public void setJpa(Map<String, Object> jpa) {
        this.dynamicProperties.put("jpa", jpa);
    }

    /**
     * 获取JPA配置属性（从动态属性映射中获取）
     *
     * @return JPA配置属性映射，如果不存在则返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getJpa() {
        return (Map<String, Object>) this.dynamicProperties.getOrDefault("jpa", new HashMap<>());
    }

    /**
     * 获取包路径配置
     * 
     * @return 包路径配置对象
     */
    public ScopePackagesProperties getPackages() {
        return packages;
    }

    /**
     * 设置包路径配置
     * 
     * @param packages 包路径配置对象
     */
    public void setPackages(ScopePackagesProperties packages) {
        this.packages = packages;
    }

    /**
     * 判断是否为主数据源
     * 
     * @return true表示是主数据源，false表示不是
     */
    public boolean isPrimary() {
        return primary;
    }

    /**
     * 设置是否为主数据源
     *
     * @param primary true表示设为主数据源，false表示不是主数据源
     */
    public void setPrimary(boolean primary) {
        this.primary = primary;
    }

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    public DataSourceType getType() {
        return type;
    }

    /**
     * 设置数据源类型
     *
     * @param type 数据源类型
     */
    public void setType(DataSourceType type) {
        this.type = type;
    }

    /**
     * 获取动态配置属性映射
     *
     * @return 动态配置属性映射
     */
    public Map<String, Object> getDynamicProperties() {
        return dynamicProperties;
    }

    /**
     * 设置动态配置属性映射
     *
     * @param dynamicProperties 动态配置属性映射
     */
    public void setDynamicProperties(Map<String, Object> dynamicProperties) {
        this.dynamicProperties = dynamicProperties;
    }

    /**
     * 设置MyBatis配置属性（通过动态属性映射）
     *
     * @param mybatis MyBatis配置属性映射
     */
    public void setMybatis(Map<String, Object> mybatis) {
        this.dynamicProperties.put("mybatis", mybatis);
    }

    /**
     * 获取MyBatis配置属性（从动态属性映射中获取）
     *
     * @return MyBatis配置属性映射，如果不存在则返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMybatis() {
        return (Map<String, Object>) this.dynamicProperties.getOrDefault("mybatis", new HashMap<>());
    }

    /**
     * 设置MyBatisPlus配置属性（通过动态属性映射）
     *
     * @param mybatisPlus MyBatisPlus配置属性映射
     */
    public void setMybatisPlus(Map<String, Object> mybatisPlus) {
        this.dynamicProperties.put("mybatis-plus", mybatisPlus);
    }

    /**
     * 获取MyBatisPlus配置属性（从动态属性映射中获取）
     *
     * @return MyBatisPlus配置属性映射，如果不存在则返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMybatisPlus() {
        return (Map<String, Object>) this.dynamicProperties.getOrDefault("mybatis-plus", new HashMap<>());
    }

    /**
     * 获取Hikari连接池配置属性
     *
     * @return Hikari连接池配置属性对象
     */
    public HikariProperties getHikari() {
        return hikari;
    }

    /**
     * 设置Hikari连接池配置属性
     *
     * @param hikari Hikari连接池配置属性对象
     */
    public void setHikari(HikariProperties hikari) {
        this.hikari = hikari;
    }
}