package cn.com.chinastock.cnf.mdatasource.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.scripting.LanguageDriver;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * MyBatis Plus配置应用工具类
 * 负责将MybatisPlusProperties中的配置应用到MybatisSqlSessionFactoryBean
 *
 * <AUTHOR>
 */
public class MybatisPlusConfigurationApplier {

    /**
     * 应用MyBatis Plus配置到MybatisSqlSessionFactoryBean
     *
     * @param factoryBean    MybatisSqlSessionFactoryBean实例
     * @param properties     MyBatis Plus配置属性
     * @param resourceLoader 资源加载器
     */
    public static void applyConfiguration(MybatisSqlSessionFactoryBean factoryBean,
                                          MybatisPlusProperties properties,
                                          ResourceLoader resourceLoader) {
        factoryBean.setVfs(SpringBootVFS.class);
        if (StringUtils.hasText(properties.getConfigLocation())) {
            factoryBean.setConfigLocation(resourceLoader.getResource(properties.getConfigLocation()));
        }

        applyConfiguration(factoryBean, properties);
        if (properties.getConfigurationProperties() != null) {
            factoryBean.setConfigurationProperties(properties.getConfigurationProperties());
        }

        if (StringUtils.hasLength(properties.getTypeAliasesPackage())) {
            factoryBean.setTypeAliasesPackage(properties.getTypeAliasesPackage());
        }

        if (properties.getTypeAliasesSuperType() != null) {
            factoryBean.setTypeAliasesSuperType(properties.getTypeAliasesSuperType());
        }

        if (StringUtils.hasLength(properties.getTypeHandlersPackage())) {
            factoryBean.setTypeHandlersPackage(properties.getTypeHandlersPackage());
        }

        if (!ObjectUtils.isEmpty(properties.resolveMapperLocations())) {
            factoryBean.setMapperLocations(properties.resolveMapperLocations());
        }

        if (properties.getDefaultScriptingLanguageDriver() != null) {
            try {
                Class<? extends LanguageDriver> driverClass = properties.getDefaultScriptingLanguageDriver();
                LanguageDriver driver = driverClass.getDeclaredConstructor().newInstance();
                factoryBean.setScriptingLanguageDrivers(driver);
            } catch (Exception e) {
            }
        }

        factoryBean.setGlobalConfig(createGlobalConfig(properties));

        if (properties.isCheckConfigLocation()) {
            factoryBean.setFailFast(true);
        }
    }

    private static void applyConfiguration(MybatisSqlSessionFactoryBean factory, MybatisPlusProperties properties) {
        MybatisPlusProperties.CoreConfiguration coreConfiguration = properties.getConfiguration();
        MybatisConfiguration configuration = null;
        if (coreConfiguration != null || !StringUtils.hasText(properties.getConfigLocation())) {
            configuration = new MybatisConfiguration();
        }
        if (configuration != null && coreConfiguration != null) {
            coreConfiguration.applyTo(configuration);
        }
        factory.setConfiguration(configuration);
    }

    /**
     * 创建并配置GlobalConfig对象
     *
     * @param properties MyBatis Plus配置属性
     * @return 配置好的GlobalConfig对象
     */
    public static GlobalConfig createGlobalConfig(MybatisPlusProperties properties) {
        GlobalConfig globalConfig = properties.getGlobalConfig();

        if (globalConfig == null) {
            globalConfig = new GlobalConfig();
        }

        return globalConfig;
    }
}
