package cn.com.chinastock.cnf.mdatasource.properties;

import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;

import java.util.Map;
import java.util.HashMap;
import java.util.Collections;

/**
 * 动态配置绑定器
 * 用于在运行时根据可用的依赖动态绑定MyBatis和MyBatisPlus配置
 * 避免在类加载时强制依赖这些框架的配置类
 *
 * <AUTHOR>
 */
public class DynamicConfigurationBinder {
    
    private final Environment environment;
    private final Binder binder;
    
    public DynamicConfigurationBinder(Environment environment) {
        this.environment = environment;
        this.binder = Binder.get(environment);
    }
    
    /**
     * 绑定数据源配置，根据可用的依赖动态处理MyBatis和MyBatisPlus配置
     *
     * @param configPrefix 配置前缀，如 "spring.galaxy-datasource"
     * @return 绑定后的数据源配置映射
     */
    public Map<String, DataSourceDefinition> bindDataSourceConfigurations(String configPrefix) {
        // 首先绑定基础配置（不包含MyBatis和MyBatisPlus）
        Bindable<Map<String, DataSourceDefinition>> mapBindable = 
            Bindable.mapOf(String.class, DataSourceDefinition.class);
        
        Map<String, DataSourceDefinition> datasourcesMap = binder.bind(configPrefix, mapBindable)
                .orElseGet(Collections::emptyMap);
        
        // 为每个数据源动态绑定MyBatis和MyBatisPlus配置
        datasourcesMap.forEach((name, definition) -> {
            bindDynamicConfigurations(configPrefix + "." + name, definition);
        });
        
        return datasourcesMap;
    }
    
    /**
     * 为单个数据源绑定动态配置
     *
     * @param configPrefix 数据源配置前缀
     * @param definition 数据源定义
     */
    private void bindDynamicConfigurations(String configPrefix, DataSourceDefinition definition) {


        // 绑定JPA配置（如果JPA依赖可用）
        if (isJpaAvailable()) {
            bindJpaConfiguration(configPrefix + ".jpa", definition);
        }

        // 绑定MyBatis配置（如果MyBatis依赖可用）
        if (isMyBatisAvailable()) {
            bindMyBatisConfiguration(configPrefix + ".mybatis", definition);
        }

        // 绑定MyBatisPlus配置（如果MyBatisPlus依赖可用）
        if (isMyBatisPlusAvailable()) {
            bindMyBatisPlusConfiguration(configPrefix + ".mybatis-plus", definition);
        }
    }
    
    /**
     * 绑定MyBatis配置
     *
     * @param configPrefix MyBatis配置前缀
     * @param definition 数据源定义
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void bindMyBatisConfiguration(String configPrefix, DataSourceDefinition definition) {
        try {
            // 使用反射创建MyBatis配置对象，避免直接依赖
            Class mybatisPropertiesClass = Class.forName("org.mybatis.spring.boot.autoconfigure.MybatisProperties");
            Object mybatisProperties = mybatisPropertiesClass.getDeclaredConstructor().newInstance();

            // 绑定配置 - 使用原始类型避免泛型问题
            Bindable bindable = Bindable.of(mybatisPropertiesClass).withExistingValue(mybatisProperties);
            binder.bind(configPrefix, bindable);

            // 将配置对象存储到动态属性中
            definition.getDynamicProperties().put("mybatis", mybatisProperties);
        } catch (Exception e) {
            // 如果绑定失败，记录警告但不中断流程
            // 这里可以添加日志记录
        }
    }
    
    /**
     * 绑定MyBatisPlus配置
     *
     * @param configPrefix MyBatisPlus配置前缀
     * @param definition 数据源定义
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void bindMyBatisPlusConfiguration(String configPrefix, DataSourceDefinition definition) {
        try {
            // 使用反射创建MyBatisPlus配置对象，避免直接依赖
            Class mybatisPlusPropertiesClass = Class.forName("com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties");
            Object mybatisPlusProperties = mybatisPlusPropertiesClass.getDeclaredConstructor().newInstance();

            // 绑定配置 - 使用原始类型避免泛型问题
            Bindable bindable = Bindable.of(mybatisPlusPropertiesClass).withExistingValue(mybatisPlusProperties);
            binder.bind(configPrefix, bindable);

            // 将配置对象存储到动态属性中
            definition.getDynamicProperties().put("mybatis-plus", mybatisPlusProperties);
        } catch (Exception e) {
            // 如果绑定失败，记录警告但不中断流程
            // 这里可以添加日志记录
        }
    }

    /**
     * 绑定JPA配置
     *
     * @param configPrefix JPA配置前缀
     * @param definition 数据源定义
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void bindJpaConfiguration(String configPrefix, DataSourceDefinition definition) {
        try {
            // 使用反射创建JPA配置对象，避免直接依赖
            Class jpaPropertiesClass = Class.forName("org.springframework.boot.autoconfigure.orm.jpa.JpaProperties");
            Object jpaProperties = jpaPropertiesClass.getDeclaredConstructor().newInstance();

            // 绑定配置 - 使用原始类型避免泛型问题
            Bindable bindable = Bindable.of(jpaPropertiesClass).withExistingValue(jpaProperties);
            Object boundResult = binder.bind(configPrefix, bindable).orElse(jpaProperties);

            // 将配置对象存储到动态属性中
            definition.getDynamicProperties().put("jpa", boundResult);
        } catch (Exception e) {
            // 如果绑定失败，记录警告但不中断流程
            // 这里可以添加日志记录
        }
    }

    /**
     * 检查JPA是否可用
     *
     * @return true表示JPA可用，false表示不可用
     */
    private boolean isJpaAvailable() {
        try {
            Class.forName("org.springframework.boot.autoconfigure.orm.jpa.JpaProperties");
            // Spring Boot 3.x 使用 Jakarta EE
            Class.forName("jakarta.persistence.EntityManager");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 检查MyBatis是否可用
     *
     * @return true表示MyBatis可用，false表示不可用
     */
    private boolean isMyBatisAvailable() {
        try {
            Class.forName("org.mybatis.spring.boot.autoconfigure.MybatisProperties");
            Class.forName("org.apache.ibatis.session.SqlSessionFactory");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 检查MyBatisPlus是否可用
     *
     * @return true表示MyBatisPlus可用，false表示不可用
     */
    private boolean isMyBatisPlusAvailable() {
        try {
            Class.forName("com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties");
            Class.forName("com.baomidou.mybatisplus.core.MybatisConfiguration");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
