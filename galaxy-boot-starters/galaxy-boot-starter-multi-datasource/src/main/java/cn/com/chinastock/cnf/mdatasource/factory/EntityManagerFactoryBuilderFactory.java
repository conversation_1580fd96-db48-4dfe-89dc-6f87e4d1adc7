package cn.com.chinastock.cnf.mdatasource.factory;

import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.util.Assert;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

public class EntityManagerFactoryBuilderFactory implements FactoryBean<EntityManagerFactory>, InitializingBean {

    private final JpaProperties jpaProperties;
    private final DataSource dataSource;
    private final String[] packagesToScan;
    private final String persistenceUnitName;

    @Autowired
    private EntityManagerFactoryBuilder builder;

    private LocalContainerEntityManagerFactoryBean entityManagerFactoryBean;

    public EntityManagerFactoryBuilderFactory(JpaProperties jpaProperties, DataSource dataSource,
                                              String[] packagesToScan, String persistenceUnitName) {
        this.jpaProperties = jpaProperties;
        this.dataSource = dataSource;
        this.packagesToScan = packagesToScan;
        this.persistenceUnitName = persistenceUnitName;
    }

    @Override
    public void afterPropertiesSet() {
        Assert.notNull(builder, "EntityManagerFactoryBuilder cannot be null.");

        // 使用 JPA 属性，Spring Boot 会自动处理 Hibernate 配置
        Map<String, Object> properties = new HashMap<>(this.jpaProperties.getProperties());

        this.entityManagerFactoryBean = builder
                .dataSource(this.dataSource)
                .packages(this.packagesToScan)
                .properties(properties)
                .persistenceUnit(this.persistenceUnitName)
                .build();

        this.entityManagerFactoryBean.afterPropertiesSet();
    }

    @Override
    public EntityManagerFactory getObject() {
        return this.entityManagerFactoryBean.getObject();
    }

    @Override
    public Class<?> getObjectType() {
        return EntityManagerFactory.class;
    }
}