package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.config.MybatisConfigurationApplier;
import cn.com.chinastock.cnf.mdatasource.factory.DataSourceFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;

/**
 * MyBatis数据源注册器
 * 负责注册MyBatis相关的Bean定义
 *
 * <AUTHOR>
 */
public class MybatisDataSourceRegistrar implements DataSourceRegistrar {
    
    @Override
    public boolean supports(DataSourceDefinition properties) {
        return properties.getType() == DataSourceType.MYBATIS;
    }

    @Override
    public void registerDataSource(BeanDefinitionRegistry registry,
                                 String datasourceName,
                                 DataSourceDefinition properties,
                                 ResourceLoader resourceLoader) {
        String dataSourceBeanName = datasourceName + "DataSource";
        String sqlSessionFactoryBeanName = datasourceName + "SqlSessionFactory";
        String sqlSessionTemplateBeanName = datasourceName + "SqlSessionTemplate";
        String transactionManagerBeanName = datasourceName + "TransactionManager";

        // 注册DataSource
        registerDataSourceBean(registry, dataSourceBeanName, properties);

        // 注册SqlSessionFactory
        registerSqlSessionFactoryBean(registry, sqlSessionFactoryBeanName,
                                    dataSourceBeanName, properties, resourceLoader);

        // 注册SqlSessionTemplate
        registerSqlSessionTemplateBean(registry, sqlSessionTemplateBeanName,
                                     sqlSessionFactoryBeanName, properties);

        // 注册TransactionManager
        registerTransactionManagerBean(registry, transactionManagerBeanName,
                                     dataSourceBeanName, properties);

        // 注册MyBatis Mappers
        registerMybatisMappers(registry, properties, sqlSessionFactoryBeanName, resourceLoader);
    }

    private void registerDataSourceBean(BeanDefinitionRegistry registry,
                                      String beanName,
                                      DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                () -> DataSourceFactory.createDataSource(properties, beanName));
        registerBean(beanName, registry, properties, builder);
    }

    private void registerSqlSessionFactoryBean(BeanDefinitionRegistry registry,
                                             String beanName,
                                             String dataSourceBeanName,
                                             DataSourceDefinition properties,
                                             ResourceLoader resourceLoader) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionFactoryBean.class,
                () -> {
                    SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();

                    // 应用MyBatis配置 - 从动态属性中获取
                    Object mybatisPropsObj = properties.getDynamicProperties().get("mybatis");
                    if (mybatisPropsObj instanceof org.mybatis.spring.boot.autoconfigure.MybatisProperties) {
                        org.mybatis.spring.boot.autoconfigure.MybatisProperties mybatisProps =
                            (org.mybatis.spring.boot.autoconfigure.MybatisProperties) mybatisPropsObj;
                        MybatisConfigurationApplier.applyConfiguration(factoryBean, mybatisProps, resourceLoader);
                    }

                    return factoryBean;
                });

        builder.addPropertyReference("dataSource", dataSourceBeanName);

        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerSqlSessionTemplateBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String sqlSessionFactoryBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(SqlSessionTemplate.class);
        builder.addConstructorArgReference(sqlSessionFactoryBeanName);

        // 设置ExecutorType（如果配置了的话）- 从动态属性中获取
        Object mybatisPropsObj = properties.getDynamicProperties().get("mybatis");
        if (mybatisPropsObj instanceof org.mybatis.spring.boot.autoconfigure.MybatisProperties) {
            org.mybatis.spring.boot.autoconfigure.MybatisProperties mybatisProps =
                (org.mybatis.spring.boot.autoconfigure.MybatisProperties) mybatisPropsObj;
            if (mybatisProps.getExecutorType() != null) {
                builder.addConstructorArgValue(mybatisProps.getExecutorType());
            }
        }

        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerTransactionManagerBean(BeanDefinitionRegistry registry,
                                              String beanName,
                                              String dataSourceBeanName,
                                              DataSourceDefinition properties) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSourceTransactionManager.class);
        builder.addConstructorArgReference(dataSourceBeanName);
        registerBean(beanName, registry, properties, builder);
    }
    
    private void registerMybatisMappers(BeanDefinitionRegistry registry,
                                      DataSourceDefinition properties,
                                      String sqlSessionFactoryBeanName,
                                      ResourceLoader resourceLoader) {
        ClassPathScanningCandidateComponentProvider scanner = new MapperScanner(resourceLoader);

        properties.getPackages().getMapper().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> mapperClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(mapperClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(MapperFactoryBean.class);
                builder.addConstructorArgValue(mapperClass);

                // 明确指定使用SqlSessionFactory，避免自动装配冲突
                builder.addPropertyReference("sqlSessionFactory", sqlSessionFactoryBeanName);

                // 禁用自动装配，避免SqlSessionTemplate冲突
                builder.setAutowireMode(AbstractBeanDefinition.AUTOWIRE_NO);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }

    private void registerBean(String beanName,
                            BeanDefinitionRegistry registry,
                            DataSourceDefinition properties,
                            BeanDefinitionBuilder builder) {
        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }
        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }
    
    private Class<?> classForName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Cannot load class: " + className, e);
        }
    }
    
    /**
     * Mapper扫描器内部类
     * 用于扫描指定包路径下的Mapper接口
     */
    private static class MapperScanner extends ClassPathScanningCandidateComponentProvider {
        
        /**
         * 构造函数
         * 
         * @param resourceLoader 资源加载器
         */
        public MapperScanner(ResourceLoader resourceLoader) {
            super(false);
            super.setResourceLoader(resourceLoader);
            super.addIncludeFilter(new AnnotationTypeFilter(Mapper.class));
        }

        /**
         * 判断是否为候选组件
         * 只有接口类型才被认为是候选组件
         * 
         * @param beanDefinition Bean定义
         * @return true表示是候选组件，false表示不是
         */
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface();
        }
    }
}
