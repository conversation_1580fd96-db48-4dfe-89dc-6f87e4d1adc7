package cn.com.chinastock.cnf.mdatasource.config;

import org.apache.ibatis.scripting.LanguageDriver;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * MyBatis配置应用工具类
 * 负责将MybatisProperties中的配置应用到SqlSessionFactoryBean
 * 
 * <AUTHOR>
 */
public class MybatisConfigurationApplier {

    /**
     * 应用MyBatis配置到SqlSessionFactoryBean
     * 
     * @param factoryBean SqlSessionFactoryBean实例
     * @param properties MyBatis配置属性
     * @param resourceLoader 资源加载器
     */
    public static void applyConfiguration(SqlSessionFactoryBean factoryBean, 
                                        MybatisProperties properties, 
                                        ResourceLoader resourceLoader) {
        if (properties.getConfiguration() == null || properties.getConfiguration().getVfsImpl() == null) {
            factoryBean.setVfs(SpringBootVFS.class);
        }

        if (StringUtils.hasText(properties.getConfigLocation())) {
            factoryBean.setConfigLocation(resourceLoader.getResource(properties.getConfigLocation()));
        }

        applyConfiguration(factoryBean, properties);
        if (properties.getConfigurationProperties() != null) {
            factoryBean.setConfigurationProperties(properties.getConfigurationProperties());
        }

        if (StringUtils.hasLength(properties.getTypeAliasesPackage())) {
            factoryBean.setTypeAliasesPackage(properties.getTypeAliasesPackage());
        }

        if (properties.getTypeAliasesSuperType() != null) {
            factoryBean.setTypeAliasesSuperType(properties.getTypeAliasesSuperType());
        }

        if (StringUtils.hasLength(properties.getTypeHandlersPackage())) {
            factoryBean.setTypeHandlersPackage(properties.getTypeHandlersPackage());
        }

        Resource[] mapperLocations = properties.resolveMapperLocations();
        if (!ObjectUtils.isEmpty(mapperLocations)) {
            factoryBean.setMapperLocations(mapperLocations);
        }

        if (properties.getDefaultScriptingLanguageDriver() != null) {
            try {
                Class<? extends LanguageDriver> driverClass = properties.getDefaultScriptingLanguageDriver();
                LanguageDriver driver = driverClass.getDeclaredConstructor().newInstance();
                factoryBean.setScriptingLanguageDrivers(driver);
            } catch (Exception e) {
            }
        }

        if (properties.isCheckConfigLocation()) {
            factoryBean.setFailFast(true);
        }
    }

    private static void applyConfiguration(SqlSessionFactoryBean factory, MybatisProperties properties) {
        MybatisProperties.CoreConfiguration coreConfiguration = properties.getConfiguration();
        org.apache.ibatis.session.Configuration configuration = null;
        if (coreConfiguration != null || !StringUtils.hasText(properties.getConfigLocation())) {
            configuration = new org.apache.ibatis.session.Configuration();
        }

        if (configuration != null && coreConfiguration != null) {
            coreConfiguration.applyTo(configuration);
        }

        factory.setConfiguration(configuration);
    }
}
