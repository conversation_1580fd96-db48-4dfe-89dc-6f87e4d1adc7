# 多数据源配置示例 - 展示完整的MyBatis和MyBatis Plus配置支持
galaxy:
  multi-datasource:
    datasources:
      # MyBatis数据源配置示例
      primary:
        primary: true
        type: MYBATIS
        datasource:
          url: jdbc:h2:mem:primary
          driver-class-name: org.h2.Driver
          username: sa
          password: 
        mybatis:
          # 基本配置
          config-location: classpath:mybatis-config.xml
          mapper-locations: 
            - classpath*:mapper/primary/**/*.xml
          type-aliases-package: com.example.primary.entity
          type-aliases-super-type: com.example.common.BaseEntity
          type-handlers-package: com.example.primary.typehandler
          executor-type: REUSE
          check-config-location: true
          default-scripting-language-driver: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
          lazy-initialization: false
          mapper-default-scope: singleton
          inject-sql-session-on-mapper-scan: true
          
          # 配置属性
          configuration-properties:
            # 数据库相关配置
            mapUnderscoreToCamelCase: true
            cacheEnabled: true
            lazyLoadingEnabled: true
            aggressiveLazyLoading: false
            multipleResultSetsEnabled: true
            useColumnLabel: true
            useGeneratedKeys: true
            
            # 执行器配置
            defaultExecutorType: SIMPLE
            defaultStatementTimeout: 30
            defaultFetchSize: 100
            
            # 安全配置
            safeRowBoundsEnabled: false
            safeResultHandlerEnabled: true
            
            # 其他配置
            callSettersOnNulls: false
            useActualParamName: true
            returnInstanceForEmptyRow: false
            logPrefix: "mybatis.primary."
            
        packages:
          entity:
            - com.example.primary.entity
          mapper:
            - com.example.primary.mapper
            
      # MyBatis Plus数据源配置示例
      secondary:
        type: MYBATIS_PLUS
        datasource:
          url: jdbc:h2:mem:secondary
          driver-class-name: org.h2.Driver
          username: sa
          password: 
        mybatis-plus:
          # 基本配置
          config-location: classpath:mybatis-plus-config.xml
          mapper-locations: 
            - classpath*:mapper/secondary/**/*.xml
          type-aliases-package: com.example.secondary.entity
          type-aliases-super-type: com.example.common.BaseEntity
          type-handlers-package: com.example.secondary.typehandler
          type-enums-package: com.example.secondary.enums
          executor-type: BATCH
          check-config-location: true
          lazy-initialization: false
          mapper-default-scope: singleton
          inject-sql-session-on-mapper-scan: true
          
          # 配置属性
          configuration-properties:
            # 数据库相关配置
            mapUnderscoreToCamelCase: true
            cacheEnabled: false
            lazyLoadingEnabled: false
            aggressiveLazyLoading: false
            multipleResultSetsEnabled: true
            useColumnLabel: true
            useGeneratedKeys: true
            
            # 执行器配置
            defaultExecutorType: BATCH
            defaultStatementTimeout: 25
            defaultFetchSize: 50
            
            # 安全配置
            safeRowBoundsEnabled: true
            safeResultHandlerEnabled: true
            
            # 其他配置
            callSettersOnNulls: true
            useActualParamName: true
            returnInstanceForEmptyRow: true
            logPrefix: "mybatis.secondary."
            
          # 全局配置
          global-config:
            # 基本配置
            banner: true
            enable-sql-runner: true
            
            # 数据库配置
            db-config:
              # 主键配置
              id-type: ASSIGN_ID
              
              # 表名配置
              table-prefix: t_
              schema: secondary_db
              table-underline: true
              capital-mode: false
              
              # 字段配置
              column-format: "%s"
              property-format: "%s"
              table-format: "%s"
              
              # 逻辑删除配置
              logic-delete-field: deleted
              logic-delete-value: 1
              logic-not-delete-value: 0
              
              # 字段策略配置
              insert-strategy: NOT_NULL
              update-strategy: NOT_NULL
              where-strategy: NOT_NULL
              
        packages:
          entity:
            - com.example.secondary.entity
          mapper:
            - com.example.secondary.mapper
            
      # 第三个数据源 - 展示更多MyBatis Plus高级配置
      tertiary:
        type: MYBATIS_PLUS
        datasource:
          url: jdbc:h2:mem:tertiary
          driver-class-name: org.h2.Driver
          username: sa
          password: 
        mybatis-plus:
          # 基本配置
          mapper-locations: 
            - classpath*:mapper/tertiary/**/*.xml
          type-aliases-package: com.example.tertiary.entity
          type-handlers-package: com.example.tertiary.typehandler
          
          # 配置属性
          configuration-properties:
            mapUnderscoreToCamelCase: true
            cacheEnabled: true
            defaultStatementTimeout: 60
            defaultFetchSize: 200
            
          # 全局配置
          global-config:
            db-config:
              # 主键配置
              id-type: AUTO
              
              # 表名配置
              table-prefix: sys_
              table-underline: false
              capital-mode: true
              
              # 逻辑删除配置
              logic-delete-field: is_deleted
              logic-delete-value: "true"
              logic-not-delete-value: "false"
              
              # 字段策略配置
              insert-strategy: NOT_EMPTY
              update-strategy: NOT_EMPTY
              where-strategy: NOT_EMPTY
              
        packages:
          entity:
            - com.example.tertiary.entity
          mapper:
            - com.example.tertiary.mapper

# 日志配置
logging:
  level:
    com.example: DEBUG
    org.mybatis: DEBUG
    com.baomidou.mybatisplus: DEBUG
