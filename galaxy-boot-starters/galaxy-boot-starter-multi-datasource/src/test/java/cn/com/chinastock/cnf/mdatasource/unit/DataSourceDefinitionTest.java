package cn.com.chinastock.cnf.mdatasource.unit;

import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceType;
import cn.com.chinastock.cnf.mdatasource.properties.ScopePackagesProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataSourceDefinition 测试类
 *
 * <AUTHOR>
 */
class DataSourceDefinitionTest {

    @Test
    void shouldSetDefaultValueCorrectly() {
        DataSourceDefinition definition = new DataSourceDefinition();
        
        assertFalse(definition.isPrimary());
        assertEquals(DataSourceType.JPA, definition.getType());
        assertNotNull(definition.getDatasource());
        assertNotNull(definition.getJpa());
        assertNotNull(definition.getDynamicProperties());
        assertNotNull(definition.getPackages());
    }

    @Test
    void shouldSetDataSourceCorrectly() {
        DataSourceDefinition definition = new DataSourceDefinition();
        
        // Test primary
        definition.setPrimary(true);
        assertTrue(definition.isPrimary());
        
        // Test type
        definition.setType(DataSourceType.MYBATIS);
        assertEquals(DataSourceType.MYBATIS, definition.getType());
        
        // Test datasource
        DataSourceProperties datasource = new DataSourceProperties();
        datasource.setUrl("********************************");
        definition.setDatasource(datasource);
        assertEquals("********************************", definition.getDatasource().getUrl());
        
        // Test JPA - 使用Map方式设置JPA配置
        Map<String, Object> jpaConfig = new HashMap<>();
        jpaConfig.put("show-sql", true);
        definition.setJpa(jpaConfig);
        assertEquals(true, definition.getJpa().get("show-sql"));

        // Test MyBatis - 使用Map方式设置MyBatis配置
        Map<String, Object> mybatisConfig = new HashMap<>();
        mybatisConfig.put("mapper-locations", new String[]{"classpath:mapper/*.xml"});
        definition.setMybatis(mybatisConfig);
        assertNotNull(definition.getMybatis().get("mapper-locations"));

        // Test packages
        ScopePackagesProperties packages = new ScopePackagesProperties();
        packages.setEntity(Arrays.asList("com.example.entity"));
        packages.setRepository(Arrays.asList("com.example.repository"));
        packages.setMapper(Arrays.asList("com.example.mapper"));
        definition.setPackages(packages);
        assertEquals(1, definition.getPackages().getEntity().size());
        assertEquals(1, definition.getPackages().getRepository().size());
        assertEquals(1, definition.getPackages().getMapper().size());
    }
}
