package cn.com.chinastock.cnf.metrics;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = GalaxyMetricsProperties.CONFIG_PREFIX)
public class GalaxyMetricsProperties {
    public static final String CONFIG_PREFIX = "galaxy.metrics";
    
    private Datasource datasource = new Datasource();

    public Datasource getDatasource() {
        return datasource;
    }

    public void setDatasource(Datasource datasource) {
        this.datasource = datasource;
    }

    public static class Datasource {
        private Prometheus prometheus = new Prometheus();

        public Prometheus getPrometheus() {
            return prometheus;
        }

        public void setPrometheus(Prometheus prometheus) {
            this.prometheus = prometheus;
        }
    }

    public static class Prometheus {
        /**
         * 是否启用 Prometheus 指标收集
         */
        private boolean enabled = false;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}
