## Galaxy Boot Starter Metrics

### 数据库连接池监控

Prometheus 自带 Datasource 的监控，但是条目较少，不够详细。因此，Galaxy Boot Starter Metrics 提供了对 HikariCP 连接池的监控。
由于 HikariCP 的监控数据是通过 Micrometer 暴露的，只能配置一种监控方式，因此：

- **日志和 Prometheus 二选一，不能同时为 True**

```yaml
galaxy:
  metrics:
    datasource:
      prometheus:
        enabled: true
```

##### 本地日志输出示例

在配置文件中开启 `logging` 监控，通过日志输出后，可以在日志中看到对应的 `hikaricp` 连接信息。

```bash
....LoggingMeterRegistry|-|hikaricp.connections{pool=HikariPool-1} value=17|
....LoggingMeterRegistry|-|hikaricp.connections.active{pool=HikariPool-1} value=0|
....LoggingMeterRegistry|-|hikaricp.connections.idle{pool=HikariPool-1} value=17|
....LoggingMeterRegistry|-|hikaricp.connections.max{pool=HikariPool-1} value=50|
....LoggingMeterRegistry|-|hikaricp.connections.min{pool=HikariPool-1} value=50|
....LoggingMeterRegistry|-|hikaricp.connections.pending{pool=HikariPool-1} value=0|
....LoggingMeterRegistry|-|hikaricp.connections.acquire{pool=HikariPool-1} throughput=0.5/s mean=0.0872s max=0.259s|
....LoggingMeterRegistry|-|hikaricp.connections.creation{pool=HikariPool-1} throughput=0.7/s mean=0.631s max=1.224s|
....LoggingMeterRegistry|-|hikaricp.connections.usage{pool=HikariPool-1} throughput=0.5/s mean=0.11s max=0.288s|
```

对于应用程序性能数据（例如，JVM 内存、HTTP 请求计数、数据库连接数等），Spring Boot Actuator 推荐的指标采样周期为 30 秒到 1 分钟。详细见：
[Spring Boot Actuator](https://docs.spring.io/spring-boot/reference/actuator/metrics.html)

当前是日志配置，默认指标采样周期（300秒）

##### 本地调试 Prometheus 示例

1、在 `application.yaml` 中配置 Prometheus 监控，通过 `/actuator/prometheus` 端点暴露。

```yaml
management:
  endpoints:
    web:
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region
  prometheus:
    metrics:
      export:
        enabled: true 
```

重新启动应用后，访问 `/actuator/prometheus` 即可看到对应的 `hikaricp` 连接信息：

```bash
# HELP hikaricp_connections Total connections
# TYPE hikaricp_connections gauge
hikaricp_connections{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 50.0
# HELP hikaricp_connections_acquire_seconds Connection acquire time
# TYPE hikaricp_connections_acquire_seconds summary
hikaricp_connections_acquire_seconds_count{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 3195
hikaricp_connections_acquire_seconds_sum{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.555
# HELP hikaricp_connections_acquire_seconds_max Connection acquire time
# TYPE hikaricp_connections_acquire_seconds_max gauge
hikaricp_connections_acquire_seconds_max{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.052
# HELP hikaricp_connections_active Active connections
# TYPE hikaricp_connections_active gauge
hikaricp_connections_active{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 10.0
# HELP hikaricp_connections_creation_seconds Connection creation time
# TYPE hikaricp_connections_creation_seconds summary
hikaricp_connections_creation_seconds_count{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 43
hikaricp_connections_creation_seconds_sum{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 21.089
# HELP hikaricp_connections_creation_seconds_max Connection creation time
# TYPE hikaricp_connections_creation_seconds_max gauge
hikaricp_connections_creation_seconds_max{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.93
# HELP hikaricp_connections_idle Idle connections
# TYPE hikaricp_connections_idle gauge
hikaricp_connections_idle{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 40.0
# HELP hikaricp_connections_max Max connections
# TYPE hikaricp_connections_max gauge
hikaricp_connections_max{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 50.0
# HELP hikaricp_connections_min Min connections
# TYPE hikaricp_connections_min gauge
hikaricp_connections_min{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 50.0
# HELP hikaricp_connections_pending Pending threads
# TYPE hikaricp_connections_pending gauge
hikaricp_connections_pending{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.0
# HELP hikaricp_connections_timeout_total Connection timeout total count
# TYPE hikaricp_connections_timeout_total counter
hikaricp_connections_timeout_total{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.0
# HELP hikaricp_connections_usage_seconds Connection usage time
# TYPE hikaricp_connections_usage_seconds summary
hikaricp_connections_usage_seconds_count{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 3185
hikaricp_connections_usage_seconds_sum{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 704.733
# HELP hikaricp_connections_usage_seconds_max Connection usage time
# TYPE hikaricp_connections_usage_seconds_max gauge
hikaricp_connections_usage_seconds_max{application="galaxy-boot-database-example-service",pool="galaxy-boot-database-example-service"} 0.6
```

2、配置 Prometheus 与 Spring Boot 应用的连接

```yaml
scrape_configs:
  - job_name: 'spring-boot'
    metrics_path: '/actuator/prometheus' # Spring Boot 2.x
    scrape_interval: 15s
    static_configs:
      - targets: [ 'localhost:8088' ]

```

3、配置 Grafana

- Grafana Spring Boot Dashboard
  官方模板： [Spring Boot 3.x](https://grafana.com/grafana/dashboards/19004-spring-boot-statistics/)
