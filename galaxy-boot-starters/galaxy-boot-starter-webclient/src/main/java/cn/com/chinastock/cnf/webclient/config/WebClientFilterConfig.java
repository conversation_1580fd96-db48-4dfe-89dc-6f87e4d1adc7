package cn.com.chinastock.cnf.webclient.config;

import cn.com.chinastock.cnf.webclient.esb.ESBProperties;
import cn.com.chinastock.cnf.webclient.filter.ESBFilterFunction;
import cn.com.chinastock.cnf.webclient.filter.TraceFilterFunction;
import cn.com.chinastock.cnf.webclient.filter.WebClientExceptionHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
@EnableConfigurationProperties(ESBProperties.class)
public class WebClientFilterConfig {

    /**
     * 创建基础的 WebClient.Builder，包含所有通用配置
     * @param esbProperties ESB 配置属性
     * @return 配置完成的 WebClient.Builder
     */
    private WebClient.Builder createBaseWebClientBuilder(ESBProperties esbProperties) {
        WebClientExceptionHandler exceptionHandler = new WebClientExceptionHandler();

        return WebClient.builder()
                .filter(new TraceFilterFunction())
                .filter(new ESBFilterFunction(esbProperties))
                .defaultStatusHandler(httpStatus -> httpStatus.isError(), exceptionHandler);
    }

    /**
     * 支持负载均衡的 WebClient.Builder
     * 用于需要服务发现和负载均衡的场景
     * @param esbProperties ESB 配置属性
     * @return 支持负载均衡的 WebClient.Builder
     */
    @Bean
    @LoadBalanced
    @Qualifier("loadBalancedWebClientBuilder")
    public WebClient.Builder loadBalancedWebClientBuilder(ESBProperties esbProperties) {
        return createBaseWebClientBuilder(esbProperties);
    }

    /**
     * 不支持负载均衡的 WebClient.Builder（默认）
     * 用于直接指定 URL 的场景
     * @param esbProperties ESB 配置属性
     * @return 不支持负载均衡的 WebClient.Builder
     */
    @Bean
    @Primary
    public WebClient.Builder webClientBuilder(ESBProperties esbProperties) {
        return createBaseWebClientBuilder(esbProperties);
    }

}