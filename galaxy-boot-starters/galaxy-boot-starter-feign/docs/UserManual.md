### 组件使用说明
`Galaxy Boot Starter Feign` 是一个基于 `Spring Cloud OpenFeign` 的封装，提供了统一的、规范化的服务调用功能。
使用方式请参考 [Spring Cloud OpenFeign 官方文档](https://spring.io/projects/spring-cloud-openfeign)。

### 组件功能介绍

#### 传递TraceContext
`Galaxy Boot Starter Feign` 会自动传递当前线程的 `TraceContext` 到远程服务，以实现分布式链路追踪。
在`Galaxy Boot Core`中，会处理请求头中的 `TraceId` 和 `ParentSpanId` (没有会自动生成），生成当前系统的`SpanId`, 并将其设置到当前线程的 MDC 中。
`Galaxy Boot Starter Feign` 会自动将当前线程的 MDC 中的 `TraceId` 和 `SpanId` 传递到远程服务。

详细的分布式链路追踪功能请参考 [日志规范]() 中的链路追踪章节。

#### ESB认证信息传递
`Galaxy Boot Starter Feign` 在识别到当前请求为ESB请求时，会自动传递ESB认证信息到远程服务。
- 识别ESB请求的方式：通过请求头中的 `Function-No` 字段，如果该字段存在则认为当前请求为ESB请求。
- 如果识别为ESB请求，并且请求头中缺少必要的ESB认证信息，则会自动从配置中获取ESB认证信息，并传递到远程服务。
- ESB认证信息的内容包括以下请求头信息：
  ```java
    public static final String CONTENT_TYPE_HEADER = "Content-Type";
    public static final String USER_HEADER = "User";
    public static final String CREATED_HEADER = "Created";
    public static final String NONCE_HEADER = "Nonce";
    public static final String PASSWORD_DIGEST_HEADER = "Password-Digest";
    public static final String CALLER_SYSTEM_CODE_HEADER = "Caller-System-Code";
  ```
  其中`User`和`Password`需要在`application.yml`中配置：
  ```yaml
  galaxy:
    feign:
      esb:
        user: user
        password: password
  ```
- ESB认证信息的生成是通过ESB提供的SDK来实现：
  ```xml
    <dependency>
        <groupId>com.chinastock.esb</groupId>
        <artifactId>auth</artifactId>
        <version>2.0.1</version>
    </dependency>
  ```

#### 异常处理
在`Galaxy Boot Core`中定义了统一的异常处理，远端服务在返回非200的HTTP状态码时，可能会在Body中返回Meta信息。
`Galaxy Boot Starter Feign` 会自动解析Meta信息，并抛出`GalaxyFeignException`异常。
开发者可以捕获该异常，并获取Meta信息，以便进行后续处理。

#### Fallback 容错机制
`Galaxy Boot Starter Feign` 基于 `Spring Cloud OpenFeign`，完全支持 fallback 容错机制。当远程服务调用失败时（如网络异常、超时、服务不可用等），可以通过 fallback 机制提供降级处理逻辑，提高系统的容错性和可用性。

##### 使用场景
- **服务降级**：当依赖的服务不可用时，返回默认值或缓存数据
- **容错处理**：网络异常、超时等情况下的兜底逻辑
- **优雅降级**：在高并发场景下，当服务压力过大时提供简化的响应

##### Fallback 配置方式

**1. 使用 fallback 属性**

首先需要启用 fallback 功能：

```yaml
spring:
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true  # 启用断路器，fallback 依赖此功能
```

添加依赖：

```xml
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
    </dependency>
```

创建 fallback 实现类：

```java
@Component
public class UserServiceFallback implements UserService {

    @Override
    public BaseResponse<User> getUserById(Long id) {
        // 返回默认用户信息或错误响应
        Meta meta = new Meta(false, "SERVICE_UNAVAILABLE", "用户服务暂时不可用");
        return new BaseResponse<>(meta, null);
    }

    @Override
    public BaseResponse<List<User>> getAllUsers() {
        // 返回空列表或缓存数据
        Meta meta = new Meta(true, "0", "success");
        return new BaseResponse<>(meta, Collections.emptyList());
    }
}
```

在 FeignClient 中配置 fallback：

```java
@FeignClient(name = "user-service", fallback = UserServiceFallback.class)
public interface UserService {

    @GetMapping("/api/users/{id}")
    BaseResponse<User> getUserById(@PathVariable("id") Long id);

    @GetMapping("/api/users")
    BaseResponse<List<User>> getAllUsers();
}
```

**2. 使用 fallbackFactory 属性（推荐）**

fallbackFactory 相比 fallback 的优势是可以获取到异常信息，便于进行更精细的错误处理和日志记录。

创建 fallbackFactory 实现类：
```java
@Component
public class UserServiceFallbackFactory implements FallbackFactory<UserService> {

    @Override
    public UserService create(Throwable cause) {
        return new UserService() {
            @Override
            public BaseResponse<User> getUserById(Long id) {
                // 可以根据异常类型进行不同的处理
                String errorCode = "SERVICE_ERROR";
                String errorMessage = "获取用户信息失败";

                if (cause instanceof java.net.SocketTimeoutException) {
                    errorCode = "TIMEOUT_ERROR";
                    errorMessage = "请求超时，请稍后重试";
                } else if (cause instanceof java.net.ConnectException) {
                    errorCode = "CONNECTION_ERROR";
                    errorMessage = "服务连接失败";
                }

                // 记录异常日志
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                    "UserService.getUserById fallback triggered, id={}, cause={}",
                    id, cause.getMessage(), cause);

                Meta meta = new Meta(false, errorCode, errorMessage);
                return new BaseResponse<>(meta, null);
            }

            @Override
            public BaseResponse<List<User>> getAllUsers() {
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                    "UserService.getAllUsers fallback triggered, cause={}",
                    cause.getMessage(), cause);

                Meta meta = new Meta(false, "SERVICE_ERROR", "获取用户列表失败");
                return new BaseResponse<>(meta, Collections.emptyList());
            }
        };
    }
}
```

在 FeignClient 中配置 fallbackFactory：

```java
@FeignClient(name = "user-service", fallbackFactory = UserServiceFallbackFactory.class)
public interface UserService {

    @GetMapping("/api/users/{id}")
    BaseResponse<User> getUserById(@PathVariable("id") Long id);

    @GetMapping("/api/users")
    BaseResponse<List<User>> getAllUsers();
}
```

##### 注意事项
1. **启用断路器**：fallback 功能依赖于断路器，需要设置 `spring.cloud.openfeign.circuitbreaker.enabled=true`
2. **添加断路器依赖**：需要添加 `spring-cloud-starter-circuitbreaker-resilience4j` 依赖
3. **Spring Bean 注册**：fallback 和 fallbackFactory 实现类需要注册为 Spring Bean（使用 `@Component` 等注解）
4. **异常处理**：fallbackFactory 可以获取异常信息，便于进行精细化的错误处理和日志记录
5. **性能考虑**：fallback 逻辑应该尽可能简单快速，避免在降级逻辑中再次调用可能失败的外部服务
6. **测试验证**：建议通过模拟网络异常、服务停机等场景来验证 fallback 逻辑的正确性
7. **理解触发条件**：明确区分网络/协议异常（会触发 fallback）和业务逻辑异常（不会触发 fallback）

### 组件配置说明
`Galaxy Boot Starter Feign`，默认使用 `OkHttp` 作为底层的 HTTP 客户端，HTTP Client 选型可以参考下文的技术选型。

#### HTTP客户端配置
- 连接池配置
```yaml
# 默认最大连接数：200
# 默认连接存活时间：900s
spring:
  cloud:
    openfeign:
      httpclient:
        maxConnections: 200 # 最大连接数
        timeToLive: 600     # 连接存活时间
        timeToLiveUnit: SECONDS # 连接存活时间单位：秒
```

- 超时配置
```yaml
# 默认连接超时时间：1000ms
# 默认读取超时时间：10000ms
spring:
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 2000  # 单位：毫秒
            readTimeout: 5000     # 单位：毫秒
            writeTimeout: 5000    # 单位：毫秒
```

- 日志配置
```yaml
# 日志级别：none, basic, headers, full
# none: 不记录任何日志
# basic: 仅记录请求方法、URL、响应状态码和执行时间
# headers: 记录 basic 级别的基础上，记录请求和响应的头信息
# full: 记录 headers 级别的基础上，记录请求和响应的正文
# 
# 默认为：none
spring:
  cloud:
    openfeign:
      client:
        config:
          default:
            loggerLevel: full

# feign的日志默认打印级别为DEBUG
# 如果Root的日志级别为INFO，只修改feign的日志级别无法看到日志
# 可以采用以下两种方式解决：
# 1. 修改Root的日志级别为DEBUG
logging:
  level:
    root: DEBUG

# 2. 显示指定FeignClient Logger的日志级别
# cn.com.chinastock.cnf.feign.examples.client.LogClient为API对应的FeignClient的全路径名称
logging:
  level:
    cn.com.chinastock.cnf.feign.examples.client.LogClient: DEBUG
```

#### 负载均衡配置
`Galaxy Boot Starter Feign` 集成了Spring Cloud LoadBalancer，提供服务负载均衡能力。

- 基础配置
```yaml
spring:
  cloud:
    loadbalancer:
      enabled: true        # 启用负载均衡，默认false
      retry:
        enabled: false     # 是否启用重试机制，默认true
```

- 服务实例配置
在不使用注册中心的情况下，可以通过以下配置手动指定服务实例：
```yaml
spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            service-name:                # 服务名称
              - uri: http://host1:port1  # 实例1地址
              - uri: http://host2:port2  # 实例2地址
```

例如配置两个服务实例：
```yaml
spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            example-service:
              - uri: http://localhost:8087
              - uri: http://localhost:8088
```

- 负载均衡策略
Spring Cloud LoadBalancer默认使用轮询(Round Robin)策略。如需自定义负载均衡策略，可以通过实现`ReactorLoadBalancer`接口来实现。

- 重试机制
当启用重试机制时(`spring.cloud.loadbalancer.retry.enabled=true`)，在请求失败时会自动重试其他实例。
重试机制通过`RetryableFeignBlockingLoadBalancerClient`实现，未启用时使用`FeignBlockingLoadBalancerClient`。

建议在生产环境中启用重试机制，以提高服务的可用性。但需要注意以下几点：
1. 重试机制仅适用于幂等的请求（GET、HEAD、OPTIONS、PUT）
2. 需要合理配置重试次数和超时时间，避免重试过多导致系统负载过高
3. 对于非幂等请求（POST、PATCH、DELETE），建议关闭重试机制

### 组件技术选型
#### HTTP Client 框架对比

**总览对比**

| **特性**          | **Java 11+ HttpClient** | **Apache HttpClient** | **OkHttp**         | **Spring WebClient**   | **Spring RestTemplate** | **Spring Cloud OpenFeign** | **Jetty HttpClient** | **AsyncHttpClient** |
|-----------------|-------------------------|-----------------------|--------------------|------------------------|-------------------------|----------------------------|----------------------|---------------------|
| **同步与异步支持**     | 支持同步与异步                 | 支持同步与异步               | 支持同步与异步            | 支持同步与异步                | 仅支持同步                   | 支持同步与异步                    | 支持同步与异步              | 支持同步与异步             |
| **性能和吞吐量**      | 性能较高，支持多路复用             | 性能较好，但较重              | 性能优秀，适合高并发场景       | 性能优秀，尤其在响应式场景          | 性能中规中矩                  | 性能中等，依赖底层实现                | 性能优秀，轻量高效            | 性能优秀，异步高效           |
| **数据格式支持**      | 无内置支持，需手动实现             | 无内置支持，需手动实现           | 无内置支持，需手动实现        | 原生支持JSON等数据格式          | 无内置支持，需手动实现             | 依赖Spring生态支持JSON等          | 无内置支持，需手动实现          | 无内置支持，需手动实现         |
| **实现复杂度**       | 中等                      | 中等                    | 中等                 | 高                      | 低                       | 低                          | 中等                   | 高                   |
| **HTTP/2支持**    | 默认支持                    | 支持(only async APIs)   | 默认支持               | 默认支持                   | 不支持                     | 依赖底层实现                     | 默认支持                 | 默认支持                |
| **WebSocket支持** | 部分支持                    | 不支持                   | 支持                 | 不支持                    | 不支持                     | 不支持                        | 支持                   | 支持                  |
| **扩展性**         | 支持拦截器，但扩展性有限            | 支持拦截器，功能强大            | 支持拦截器，扩展性优秀        | 支持拦截器，扩展性优秀            | 不支持拦截器，扩展性较差            | 基于Spring生态，扩展性强            | 支持拦截器，扩展性优秀          | 支持拦截器，扩展性优秀         |
| **易用性**         | API简单，文档较完整             | API复杂，文档完整            | API简单，文档完整         | API简单，文档完整             | API简单，文档完整              | 依赖Spring生态，易于使用            | API复杂，文档完整           | API简单，文档完整          |
| **可靠性**         | 支持超时控制，未直接集成熔断限流        | 支持超时控制，未直接集成熔断限流      | 支持超时控制，未直接集成熔断限流   | 支持超时控制，易与熔断限流集成        | 支持超时控制，但无熔断限流支持         | 易与熔断限流集成                   | 支持超时控制，未直接集成熔断限流     | 支持超时控制，未直接集成熔断限流    |
| **安全情况**        | 活跃维护，安全漏洞较少             | 活跃维护，安全漏洞较少           | 活跃维护，安全漏洞较少        | 活跃维护，安全漏洞较少            | 安全更新较少                  | 活跃维护，安全漏洞较少                | 活跃维护，安全漏洞较少          | 活跃维护，安全漏洞较少         |
| **社区活跃度**       | 较新（2018年首次发布），活跃度中等     | 活跃（2004年首次发布），成熟度高    | 活跃（2014年首次发布），成熟度高 | 活跃（2017年首次发布），依赖Spring | 活跃（2009年首次发布），依赖Spring  | 活跃（2017年首次发布），依赖Spring     | 活跃（2000年首次发布），成熟度高   | 活跃（2010年首次发布），成熟度高  |

#### 选择建议

- **高性能场景首选**：OkHttp、Spring WebClient、Jetty HttpClient 和 AsyncHttpClient，特别是需要 HTTP/2 支持时。
- **传统同步调用场景**：Apache HttpClient 和 Spring RestTemplate，适合已有大量历史项目的集成，并发要求不高，可以提供稳定的同步调用能力。
- **轻量化与标准化需求**：Java 11+ HttpClient 适合需要轻量化 HTTP 客户端实现、符合标准 API 的场景，尤其适用于通用 Java
  开发项目，没有引入Spring等框架的场景。
- **基于 Spring 生态的选择**：
    - **简单调用**：Spring RestTemplate，代码简单，适合小型服务和传统架构。
    - **现代化服务调用**：Spring WebClient，适合需要异步或响应式支持的场景，功能强大但实现复杂度较高。
    - **声明式服务调用**：Spring Cloud OpenFeign，适合服务间调用频繁的微服务架构，开发效率高。

#### 最终选型
基于SpringBoot框架，对性能有较高要求，本框架的选型为: `Spring Cloud OpenFeign` + `OkHttp` 组合。

