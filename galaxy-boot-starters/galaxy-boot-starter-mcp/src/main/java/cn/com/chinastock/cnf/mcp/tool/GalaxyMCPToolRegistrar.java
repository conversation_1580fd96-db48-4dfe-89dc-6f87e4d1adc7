package cn.com.chinastock.cnf.mcp.tool;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import io.modelcontextprotocol.server.McpSyncServer;
import org.springframework.ai.mcp.McpToolUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Galaxy MCP Tool 注册器
 * <p>
 * 负责扫描和注册被 @Tool 注解标记的方法，将其转换为 Spring AI MCP Tool
 * 使用 Spring AI 的 MethodToolCallbackProvider 来自动处理工具注册
 * </p>
 *
 * <AUTHOR> Boot Team
 */
@Component
public class GalaxyMCPToolRegistrar implements ApplicationContextAware, SmartInitializingSingleton {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(GalaxyMCPToolRegistrar.class);

    private ApplicationContext applicationContext;
    private final Map<String, ToolCallbackProvider> toolCallbackProviders = new ConcurrentHashMap<>();
    private McpSyncServer mcpSyncServer;

    public GalaxyMCPToolRegistrar(McpSyncServer mcpSyncServer) {
        logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy MCP: Creating Galaxy MCP Tool Registrar");
        this.mcpSyncServer = mcpSyncServer;
        if (mcpSyncServer == null) {
            logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy MCP: McpSyncServer is null, dynamic tool registration disabled");
        } else {
            logger.info(LogCategory.FRAMEWORK_LOG, "Galaxy MCP: McpSyncServer available, dynamic tool registration enabled");
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterSingletonsInstantiated() {
        logger.info(LogCategory.FRAMEWORK_LOG,
                "Galaxy MCP: All singleton beans initialized. Tool registration completed with {} providers containing {} total tools",
                toolCallbackProviders.size(), getTotalToolCount());

        scanAndRegisterMCPTools();
    }

    /**
     * 执行完整扫描，确保没有遗漏任何 Controller
     * <p>
     * 这个方法会在应用上下文完全刷新且所有单例 Bean 初始化完成后执行，
     * 通过 ApplicationContext 获取所有 Controller Bean 并检查是否有遗漏的工具
     * </p>
     */
    private synchronized void scanAndRegisterMCPTools() {
        logger.info(LogCategory.FRAMEWORK_LOG,
                "Galaxy MCP: Performing full scan for Controller beans with @Tool annotations");

        try {
            Map<String, Object> controllerBeans = getControllerBeans();
            for (Map.Entry<String, Object> entry : controllerBeans.entrySet()) {
                Object bean = entry.getValue();
                Class<?> beanClass = bean.getClass();
                if (!toolCallbackProviders.containsKey(beanClass.getSimpleName())) {
                    registerToolCallbackProvider(bean, beanClass);
                }
            }

            logger.info(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: Full scan completed. Found {} new providers with {} total tools. ",
                    toolCallbackProviders.size(), getTotalToolCount());

        } catch (Exception e) {
            logger.error(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: Error during full scan", e);
        }
    }

    private Map<String, Object> getControllerBeans() {
        Map<String, Object> controllerBeans = new HashMap<>();
        controllerBeans.putAll(applicationContext.getBeansWithAnnotation(org.springframework.stereotype.Controller.class));
        controllerBeans.putAll(applicationContext.getBeansWithAnnotation(org.springframework.web.bind.annotation.RestController.class));
        return controllerBeans;
    }

    private void registerToolCallbackProvider(Object bean, Class<?> beanClass) {
        try {
            int toolMethodCount = getToolMethodCount(beanClass);
            if (toolMethodCount == 0) {
                return;
            }
            // 使用 Spring AI 的 MethodToolCallbackProvider 来自动处理工具注册
            ToolCallbackProvider toolCallbackProvider = MethodToolCallbackProvider.builder()
                    .toolObjects(bean)
                    .build();
            // 本地记录ToolCallbackProvider
            String beanName = beanClass.getSimpleName();
            toolCallbackProviders.put(beanName, toolCallbackProvider);
            // 动态注册到McpSyncServer中
            dynamicallyAddToolsToMcpServer(toolCallbackProvider, beanName);

            logger.info(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: Registered ToolCallbackProvider for bean '{}' with {} tool methods",
                    beanName, toolMethodCount);
        } catch (Exception e) {
            logger.error(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: Failed to register ToolCallbackProvider for bean " + beanClass.getSimpleName(), e);
        }
    }

    private void dynamicallyAddToolsToMcpServer(ToolCallbackProvider toolCallbackProvider, String beanName) {
        if (mcpSyncServer == null) {
            logger.error(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: McpSyncServer is null, skipping dynamic tool registration for bean '{}'", beanName);
            return;
        }

        try {
            ToolCallback[] callbacks = toolCallbackProvider.getToolCallbacks();
            if (callbacks.length > 0) {
                // 使用 McpToolUtils 转换为 MCP 工具规范并添加到服务器
                McpToolUtils.toSyncToolSpecification(Arrays.asList(callbacks))
                        .forEach(toolSpec -> {
                            try {
                                mcpSyncServer.addTool(toolSpec);
                                logger.info(LogCategory.FRAMEWORK_LOG,
                                        "Galaxy MCP: Successfully added tool '{}' from bean '{}'", toolSpec.tool().name(), beanName);
                            } catch (Exception e) {
                                logger.warn(LogCategory.FRAMEWORK_LOG,
                                        "Galaxy MCP: Failed to add tool from bean '{}' to McpSyncServer: {}", beanName, e.getMessage());
                            }
                        });
            }
        } catch (Exception e) {
            logger.error(LogCategory.FRAMEWORK_LOG,
                    "Galaxy MCP: Failed to dynamically add tools from bean '" + beanName + "' to McpSyncServer", e);
        }
    }

    /**
     * 获取指定类中@Tool注解方法的数量
     *
     * @param clazz 要检查的类
     * @return @Tool注解方法的数量
     */
    int getToolMethodCount(Class<?> clazz) {
        Method[] methods = ReflectionUtils.getAllDeclaredMethods(clazz);
        int count = 0;

        for (Method method : methods) {
            Tool toolAnnotation = AnnotationUtils.findAnnotation(method, Tool.class);
            if (toolAnnotation != null) {
                count++;
            }
        }

        return count;
    }

    private int getTotalToolCount() {
        int totalCount = 0;
        for (ToolCallbackProvider provider : toolCallbackProviders.values()) {
            ToolCallback[] callbacks = provider.getToolCallbacks();
            totalCount += callbacks.length;
        }
        return totalCount;
    }
}
