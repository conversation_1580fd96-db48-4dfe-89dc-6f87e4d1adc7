## GalaxyBoot 日志

### 组件介绍
Galaxy Boot 日志组件遵循 Galaxy日志规范，提供了统一的、规范化的日志功能。

基于信创的要求，以及目前各个系统的情况，日志的使用场景上大致分为两种情况：  
- 一种是基于东方通`TongWeb`构建的应用服务。
- 另一种是基于`Webflux`构建的支持高并发需求的应用服务，这种场景的占比大概在2%。

这两个场景对于日志链路追踪实现的方式有很大差异：  
- `TongWeb`场景建议采用`MDC`的方式，开销小。
- `Webflux`场景建议采用`Reactor Context`，线程无关，适合异步流模型。

考虑到`Webflux`场景的占比较小，对于日志功能的封装将采取以下方案：  
- 在`galaxy-boot-core`中只考虑`TongWeb`类似的同步场景。
- 对于`WebFlux`场景暂不支持，需要在`WebFlux`对应的组件中封装对于异步流模式的支持。

### 组件实例教程

#### 1. 日志的使用

##### -（以下内容仅限webmvc框架）

引用`GalaxyLogger`
```java
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
```

`GalaxyLogger`提供了`debug`、`info`、`warn`、`error`等方法，可以根据不同的场景打印不同级别的日志，具体参考`GalaxyLogger`的定义。

打印日志参考示例：
- 业务日志，INFO级别：
```java
// 指定LogCatetory打印日志
GalaxyLogger.info(LogCategory.BUSINESS_LOG, "生成8位ID: {}", id);
// 使用默认的LogCategory打印日志（默认值需要配置）
GalaxyLogger.info("生成8位ID: {}", id);
```
- 业务日志，DEBUG级别：
```java
// 指定LogCatetory打印日志
GalaxyLogger.debug(LogCategory.BUSINESS_LOG, "请求ID: {}", idDTO.getId());
// 使用默认的LogCategory打印日志（默认值需要配置）
GalaxyLogger.debug("请求ID: {}", idDTO.getId());
```
- 业务日志，ERROR级别：
```java
// 指定LogCatetory打印日志
GalaxyLogger.error(LogCategory.BUSINESS_LOG, "请求ID长度不支持, length={}", idDTO.getId().length());
// 使用默认的LogCategory打印日志（默认值需要配置）
GalaxyLogger.error("请求ID长度不支持, length={}", idDTO.getId().length());
```
- 异常日志，ERROR级别：
```java
// 指定LogCatetory打印日志
GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "生成ID时发生异常", e);
// 使用默认的LogCategory打印日志（默认值需要配置）
GalaxyLogger.error("生成ID时发生异常", e);
```

##### -（以下内容兼容 webmvc 和 webflux 框架，新项目推荐如下用法）

引用`IGalaxyLogger`
```java
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
```
在类中定义`logger`变量
```java
// 定义实例变量
private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());
// 或定义静态变量
private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(Xxx.class);
```

`IGalaxyLogger`提供了`debug`、`info`、`warn`、`error`等方法，可以根据不同的场景打印不同级别的日志，具体参考`IGalaxyLogger`的定义。

打印日志参考示例：
- 与 GalaxyLogger 使用方式的唯一区别是将GalaxyLogger.info 方法调用改为 logger.info，如：
```java
GalaxyLogger.info(LogCategory.BUSINESS_LOG, "生成8位ID: {}", id);
```
替换为
```java
logger.info(LogCategory.BUSINESS_LOG, "生成8位ID: {}", id);
```

#### 2. 日志的相关配置
- 系统的三字码配置和服务名称配置
```yaml
# 系统的三字码
galaxy:
  system:
    code: CNF

# 服务名称
spring:
  application:
    name: galaxy-boot-playbook-service
```

**重点说明**：  
为保证日志能够正确地在MOP平台上进行检索，并且能够正确地进行转储和分级，必须在`application.yml`或`application.properties`中配置`galaxy.system.code`和`spring.application.name`。  
这两个配置会在服务启动时设置到System Property中，用于日志的打印。    
考虑到三字码基本不会变化，系统的三字码暂不支持Apollo的热更新。


- 默认的日志类型配置
```yaml
# 默认的日志类型
galaxy:
  log:
    default-category: APP_LOG # 需要结合公司运营日志要求进行设置
```

- HTTP请求、响应的日志打印可以通过配置来控制，默认打印。
```yaml

galaxy:
  log:
    request-response:
      enabled: true # 是否打印RequestLog和ResponseLog，默认为true
      mask-field: true # 是否开启RequestLog和ResponseLog中字段的掩码处理，默认为false。设置为true后，被注解@MaskedField标记的字段会被掩码处理为"***"
      request-headers: true # 是否打印Request Header，默认不打印(仅在request-response.enabled=true时生效)
      response-headers: true # 是否打印Response Header，默认不打印(仅在request-response.enabled=true时生效)
```

- 性能日志的打印可以通过配置来控制，默认打印。
```yaml
# 是否打印PerformanceLogMessage，默认打印
galaxy:
  log:
    performance:
      enabled: true
```

- 日志打印的长度通过配置来控制，默认5K，如果有日志超出限制，可以通过配置来调整。
```yaml
# 日志打印的长度限制，默认5K
galaxy:
  log:
    max-length: 5000
```

- 异常日志的控制台可读版本打印
```yaml
# 是否打印异常日志的控制台可读版本，默认为false
# 参照日志规范，为了便于日志采集，每条日志打印为一行，不换行
# 为了方便开发调试，可以将异常日志打印为控制台可读版本
# 配置后，在异常发生时，会额外打印一条可读版本的异常堆栈信息
galaxy:
  log:
    exception-pretty-print: true
```

#### 3. 配置动态加载
日志的动态加载目前是依赖于Apollo配置中心的，需要在Apollo配置中心中配置日志的相关配置，然后在应用启动时加载配置。
Apollo的配置：
```yaml
app:
  id: galaxy-boot # 应用ID
apollo:
  meta: http://localhost:8080 # Apollo配置中心地址
```

更新`Root`的日志级别，在Apollo配置中心中增加如下配置并发布，即可动态更新日志级别为`DEBUG`：
```
KEY = logging.level.root
VALUE = DEBUG
```

对于`galaxy-boot-starter-feign`中指定Feign Client的Logger，如果希望在Root Logger日志级别为INFO时打开，也可以通过如下配置修改：
```
KEY = logging.level.cn.com.chinastock.cnf.examples.client.ESBClient
VALUE = DEBUG
```

在`2. 日志相关配置`中定义的配置，除了`galaxy.system.code`外，其余配置都可以通过Apollo配置中心进行动态加载。

#### 4. 日志的异步打印相关配置
在工程文件`log4j2.component.properties`中定义了异步相关配置
```
log4j2.contextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector
log4j2.asyncLoggerRingBufferSize=32768 # 异步日志的环形缓冲区大小
log4j2.asyncQueueFullPolicy=Discard    # 当环形缓冲区满时，控制如何处理新到的日志事件
```

如果希望修改这些配置，可以通过在启动进程时加入JVM运行参数来修改，示例如下：
```
java -jar -Dlog4j2.asyncLoggerRingBufferSize=1024 xxx.jar
```

#### 5. 日志输出到文件
在`log4j2.xml`中定义了输出到文件的配置，默认是不开启文件的输出，只输出到控制台。  
- 如果要启用日志文件输出，需要在程序启动时增加参数`-Dlog.file.enabled=ALL`，示例如下：
```
# 日志默认输出到当前目录的logs目录下，可以通过`-Dlog.path`来设置日志的输出目录：
java -jar -Dlog.file.enabled=ALL -Dlog.path=/var/logs xxx.jar
```
- 输出的文件名：
```
目前的日志输出分为三类:
第一类是普通的日志，INFO级别的日志，会输出到文件: `application.log`
第二类是错误日志，ERROR级别的日志，会输出到文件: `error.log`
第三类是SQL日志，会输出到文件: `sql.log` (由于SQL日志采用不同的Appender，避免转存问题，输出到单独的文件中）
```
- 文件的转存策略：
```
1. 滚动规则：
- 每天滚动一次日志文件。
- 或当日志文件大小达到 100 MB 时立即滚动。
2. 保留规则：
- 最多保留 30 个滚动文件。
- 删除最后修改时间超过 30 天的旧日志文件。
```

#### 6. `SQL`日志的打印
`Galaxy Boot`框架提供了`SQL`日志的打印功能。

##### 6.1 针对`Spring Data JPA`的日志打印配置：
- 程序中如果使用`Spring Data JPA`，`SQL`日志是默认打印的，输出示例日志如下：  

  ```
  V1|2025-01-16T16:58:52.172+0800|...|org.hibernate.SQL|SQL_LOG|insert into blog (author,content,created_time,title,updated_time) values (?,?,?,?,?)|-
  V1|2025-01-16T16:58:52.176+0800|...|org.hibernate.orm.jdbc.bind|SQL_LOG|binding parameter (1:VARCHAR) <- [author-1737017931876]|-
  V1|2025-01-16T16:58:52.176+0800|...|org.hibernate.orm.jdbc.bind|SQL_LOG|binding parameter (2:VARCHAR) <- [content-1737017931876]|-
  V1|2025-01-16T16:58:52.176+0800|...|org.hibernate.orm.jdbc.bind|SQL_LOG|binding parameter (3:DATE) <- [null]|-
  V1|2025-01-16T16:58:52.176+0800|...|org.hibernate.orm.jdbc.bind|SQL_LOG|binding parameter (4:VARCHAR) <- [title-1737017931876]|-
  V1|2025-01-16T16:58:52.176+0800|...|org.hibernate.orm.jdbc.bind|SQL_LOG|binding parameter (5:DATE) <- [null]|-
  ```

- 关闭`Spring Data JPA`的日志输出：
  在`application.yml`中设置`logging.level.org.hibernate.SQL`的日志级别为`OFF`或高于`DEBUG`即可关闭`Spring Data JPA`的日志输出
  ```
  logging:
    level:
      org.hibernate.SQL: OFF
      org.hibernate.orm.jdbc.bind: OFF
  
  # 在jdk8的版本中，参数绑定的日志输出级别设置为`org.hibernate.type.descriptor.sql.BasicBinder`
  logging:
    level:
      org.hibernate.type.descriptor.sql.BasicBinder: OFF
  ```


##### 6.2 针对`MyBatis`的日志打印配置：
如果程序中使用了`MyBatis`，日志打印需要进行如下配置：

- 首先在`mybatis-config.xml`中指定使用`Log4j2`为日志输出实现，并配置日志输出`LOGGER`的前缀，配置如下：  

  ```
  <configuration>
      <settings>
          <setting name="logImpl" value="LOG4J2"/>
          <setting name="logPrefix" value="SQL."/>
      </settings>
  </configuration>
  ```

- 然后在`application.yml`中指定该配置文件的位置：  

  ```
  mybatis:
      config-location: classpath:mybatis-config.xml
  ```

- 日志输出示例：

  ```
  V1|2025-01-16T16:54:06.742+0800|...|SQL.XXX.BlogMapper.save|SQL_LOG|==>  Preparing: INSERT INTO blog (title, author, content) VALUES (?, ?, ?)|-
  V1|2025-01-16T16:54:06.743+0800|...|SQL.XXX.BlogMapper.save|SQL_LOG|==> Parameters: title-1737017646598(String), author-1737017646598(String), content-1737017646598(String)|-
  V1|2025-01-16T16:54:06.989+0800|...|SQL.XXX.BlogMapper.save|SQL_LOG|<==    Updates: 1|-
  ```

- 关闭`MyBatis`的日志输出：
  在`mybatis-config.xml`中设置了`LOGGER`会以`SQL.`为前缀，因此配置`SQL`的日志级别为`OFF`或高于`DEBUG`即可关闭`MyBatis`的日志输出
  ```
  logging:
    level:
      SQL: OFF
  ```

#### 7. 线程池中日志打印

在一些场景下，会使用线程池来进行批量处理，线程池中的处理逻辑如果需要打印日志，默认是无法打印出TraceId等信息的。

Galaxy Boot 日志组件的实现依赖了 Micrometer Tracing，可以通过相对简单的方式让线程池中运行的代码逻辑也可以打印出正确的日志信息。

##### JDK 21 + Spring Boot 3.X

1. 引入 `context-propagation` 依赖

```xml
  <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>context-propagation</artifactId>
  </dependency>
```

2. 在创建线程池时，使用 `ContextExecutorService.wrap()` 包装线程池，示例代码如下：

```java
  @Bean(name = "messageProcessorExecutor")
  public ExecutorService executorService() {
      return ContextExecutorService.wrap(Executors.newFixedThreadPool(10));
  }
```

这样在线程池中运行的代码逻辑中，打印日志就会包含正确的 TraceId 等信息。

##### JDK 8 + Spring Boot 2.X

由于 Spring Boot 2.X 对 Micrometer Tracing 的支持相对有限，因此无法简单使用 `ContextExecutorService.wrap()` 的方式来实现这一功能。

在 galaxy-boot-core-jdk8 中，简单封装了 `GalaxyThreadPoolExecutor` 用来创建新的线程池，在提交任务时，会自动将当前线程的上下文信息传递到新的线程中。

```java
  @Bean(name = "messageProcessorExecutor")
  public ExecutorService executorService() {
      return new GalaxyThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
  }
```

这样在线程池中运行的代码逻辑中，打印日志就会包含正确的 TraceId 等信息。

#### 8. 特殊说明
在`REQUEST_LOG`和`RESPONSE_LOG`日志中，只对 `Content-type` 为文本类型的请求和响应打印`body`信息，这些 `Content-type` 包括：
```
"application/json": MediaType.APPLICATION_JSON_VALUE,
"application/xml": MediaType.APPLICATION_XML_VALUE,
"text/plain": MediaType.TEXT_PLAIN_VALUE,
"text/xml": MediaType.TEXT_XML_VALUE,
"application/x-www-form-urlencoded": MediaType.APPLICATION_FORM_URLENCODED_VALUE
```

对于文件上传和下载的场景，在请求和响应日志的`body`字段中不会打印文件内容，只会打印文件的元数据信息：  
- 文件上传请求，`Content-type` 为 `multipart/form-data`，`body`字段中只会打印文件的元数据信息，示例如下：  
```
body=[{"file_name":"test.pdf","file_size":"1.0MB"},{"file_name":"photo.jpg","file_size":"2.0KB"}]
```
- 文件下载请求，`Content-type` 为 `application/octet-stream`，`body`字段中只会打印文件的元数据信息（从`Content-Disposition`请求头中提取），示例如下：  
```
body={"file_name":"test.pdf","file_size":"1.0KB"}
```

#### 9. 常见问题
- 为什么日志启动时有几条日志没有打印出IP地址？  
答：这是因为IP地址是在系统启动后自动加载`LoggingAutoConfiguration`时获取，最开始的几条日志中，IP地址还没有被获取到，所以打印的是`${sys:IP_ADDR}`，在后续的日志中会被替换为真实的IP地址。
```
V1|2025-01-13T14:30:14.336+0800|196068225852666|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|cn.com.chinastock.cnf.examples.CoreExampleApplication|-|Starting CoreExampleApplication using Java 21.0.2 with PID 12467|-
V1|2025-01-13T14:30:14.337+0800|196068226977083|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|cn.com.chinastock.cnf.examples.CoreExampleApplication|-|No active profile set, falling back to 1 default profile: "default"|-
V1|2025-01-13T14:30:14.737+0800|196068626977791|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|org.springframework.data.repository.config.RepositoryConfigurationDelegate|-|Bootstrapping Spring Data JPA repositories in DEFAULT mode.|-
V1|2025-01-13T14:30:14.747+0800|196068637169583|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|org.springframework.data.repository.config.RepositoryConfigurationDelegate|-|Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.|-
V1|2025-01-13T14:30:14.819+0800|196068709104333|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider|-|App ID is set to galaxy-boot by app.id property from System Property|-
V1|2025-01-13T14:30:14.819+0800|196068709138125|WARN|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider|-|app.label is not available from System Property and /META-INF/app.properties. It is set to null|-
V1|2025-01-13T14:30:14.820+0800|196068709750250|INFO|main|**${sys:IP_ADDR}**|-|-|-|-|-|-|-|com.ctrip.framework.foundation.internals.provider.DefaultServerProvider|-|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.|-
V1|2025-01-13T14:30:14.880+0800|196068770146791|INFO|main|***********|-|-|-|-|-|-|-|org.springframework.cloud.context.scope.GenericScope|-|BeanFactory id=56813332-5602-36f3-b7ab-9dc369a64e6d|-
V1|2025-01-13T14:30:14.905+0800|196068795003375|INFO|main|***********|-|-|-|-|-|-|-|com.ctrip.framework.apollo.internals.ConfigServiceLocator|-|Located config services from apollo.config-service configuration: http://localhost:8080, will not refresh config services from remote meta service!|-
V1|2025-01-13T14:30:14.920+0800|196068809964083|WARN|main|***********|-|-|-|-|-|-|-|com.ctrip.framework.apollo.internals.RemoteConfigRepository|-|Load config failed, will retry in 1 SECONDS. appId: galaxy-boot, cluster: default, namespaces: application|-
```

- 如何通过日志查看目前引入的`Galaxy Boot`框架的版本？    
答：在服务启动日志中，会打印`Galaxy Boot`框架的版本信息，例如：
```
V1|2025-01-13T14:37:23.186+0800|...|FRAMEWORK_LOG|GalaxyBoot: FrameworkVersion=0.0.12|-
V1|2025-01-13T14:37:23.186+0800|...|FRAMEWORK_LOG|GalaxyBoot: SpringFrameworkVersion=6.1.16|-
```

- 开启了`mask-field: true`配置后，在`RESPONSE_LOG`中打印的`headers`信息不全？  
答：`mask-field: true`开启字段的掩码功能，实现方式是通过`ResponseBodyAdvice`来获取 `body` 进行再加工。从时序上比较靠前，某些响应头（如由 Filter、拦截器或容器自动生成的头）会在后续流程中才写入或修改，因此在 `ResponseBodyAdvice` 中尚无法获取到所有的 `headers`。
但在应用端返回时主动添加的 `headers` 是可以获取到并打印在日志中的。

- 开启了`mask-field: true`配置后，为什么请求和响应中有些`JSON`字段没有打印出来？  
  答：`mask-field: true`开启字段的掩码功能，为了减少对日志存储空间的占用，对`body`对象的打印会过滤掉值为`null`的字段。
