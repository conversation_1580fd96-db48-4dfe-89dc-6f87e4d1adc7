<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot-examples</artifactId>
    <packaging>pom</packaging>
    <name>Galaxy Boot Examples</name>

    <modules>
        <module>galaxy-boot-core-example</module>
        <module>galaxy-boot-database-example</module>
        <module>galaxy-boot-database-mybatis-example</module>
        <module>galaxy-boot-database-mybatisplus-example</module>
        <module>galaxy-boot-feign-example</module>
        <module>galaxy-boot-kafka-example</module>
        <module>galaxy-boot-redis-example</module>
        <module>galaxy-boot-webclient-example</module>
        <module>galaxy-boot-multi-datasource-example</module>
        <module>galaxy-boot-md-jpa-example</module>
        <module>galaxy-boot-md-mybatis-example</module>
        <module>galaxy-boot-md-mybatisplus-example</module>
        <module>galaxy-boot-webflux-example</module>
        <module>galaxy-boot-webflux-tongweb-example</module>
        <module>galaxy-boot-cache-example</module>
        <module>galaxy-boot-mcp-example</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!--Exclude javadoc jars-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!--Exclude sources jars-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <skipSource>true</skipSource>
                    <defaultManifestFile/>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>