server:
  port: 8088

spring:
  application:
    name: galaxy-boot-feign-example-service

  cloud:
    loadbalancer:
      enabled: true
      retry:
        enabled: false
    discovery:
      client:
        simple:
          instances:
            exception-service:
              - uri: http://localhost:8087
              - uri: http://localhost:8088
            log-service:
              - uri: http://localhost:8087
              - uri: http://localhost:8088
            fallback-test-service:
              - uri: http://non-existent-host:9999  # 故意配置不存在的服务地址来触发 fallback
    openfeign:
      circuitbreaker:
        enabled: true  # 启用断路器，fallback 依赖此功能
      client:
        config:
          default:
            readTimeout: 1500
            loggerLevel: full

galaxy:
  system:
    code: FXP

  log:
    request-response:
      enabled: true
      request-headers: true
    performance:
      enabled: true
    default-category: APP_LOG
    exception-pretty-print: true

logging:
  level:
    cn.com.chinastock.cnf.feign.examples.client.LogClient: DEBUG
