package cn.com.chinastock.cnf.feign.examples.client.fallback;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.feign.examples.client.LogClient;
import cn.com.chinastock.cnf.feign.examples.dto.LogDTO;
import org.springframework.stereotype.Component;

@Component
public class LogClientFallback implements LogClient {

    @Override
    public void logException() {
        GalaxyLogger.warn(LogCategory.APP_LOG, "LogClient.logException fallback triggered - 日志异常接口调用失败，执行降级逻辑");
        // 降级逻辑：静默处理，不抛出异常
    }

    @Override
    public void notFound() {
        GalaxyLogger.warn(LogCategory.APP_LOG, "LogClient.notFound fallback triggered - 404接口调用失败，执行降级逻辑");
        // 降级逻辑：静默处理，不抛出异常
    }

    @Override
    public LogDTO multiLine() {
        GalaxyLogger.warn(LogCategory.APP_LOG, "LogClient.multiLine fallback triggered - 多行日志接口调用失败，返回默认数据");
        
        // 返回默认的 LogDTO 对象
        LogDTO fallbackDto = new LogDTO();
        fallbackDto.setMessage("服务暂时不可用，这是降级后的默认消息");
        fallbackDto.setLevel("WARN");
        fallbackDto.setTimestamp(System.currentTimeMillis());
        
        return fallbackDto;
    }

    @Override
    public String gzipResponse(int length) {
        GalaxyLogger.warn(LogCategory.APP_LOG, "LogClient.gzipResponse fallback triggered - 接口调用失败，执行降级逻辑");
        // 降级逻辑：静默处理，不抛出异常
        return "接口调用失败，执行降级逻辑";
    }
}
