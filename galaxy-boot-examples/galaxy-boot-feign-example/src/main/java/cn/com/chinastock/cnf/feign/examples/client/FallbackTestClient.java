package cn.com.chinastock.cnf.feign.examples.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.feign.examples.client.fallback.FallbackTestClientFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


@FeignClient(name = "fallback-test-service", fallbackFactory = FallbackTestClientFallbackFactory.class)
public interface FallbackTestClient {

    /**
     * 测试基本的 GET 请求 fallback
     * @return 响应结果，失败时会触发 fallback
     */
    @GetMapping("/api/test")
    BaseResponse<String> testGet();

    /**
     * 测试带参数的请求 fallback
     * @param id 测试参数
     * @return 响应结果，失败时会触发 fallback
     */
    @GetMapping("/api/test/{id}")
    BaseResponse<String> testGetWithParam(@PathVariable("id") Long id);

    /**
     * 测试可能超时的请求
     * @return 响应结果，失败时会触发 fallback
     */
    @GetMapping("/api/test/slow")
    BaseResponse<String> testSlowRequest();
}
