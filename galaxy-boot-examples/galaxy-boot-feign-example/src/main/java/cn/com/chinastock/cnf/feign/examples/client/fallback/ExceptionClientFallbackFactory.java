package cn.com.chinastock.cnf.feign.examples.client.fallback;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.feign.examples.client.ExceptionClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.net.ConnectException;
import java.net.SocketTimeoutException;


@Component
public class ExceptionClientFallbackFactory implements FallbackFactory<ExceptionClient> {

    @Override
    public ExceptionClient create(Throwable cause) {
        return new ExceptionClient() {
            @Override
            public BaseResponse<Object> forbidden() {
                // 根据异常类型进行不同的处理
                String errorCode = "SERVICE_ERROR";
                String errorMessage = "调用异常服务失败";
                
                if (cause instanceof SocketTimeoutException) {
                    errorCode = "TIMEOUT_ERROR";
                    errorMessage = "请求超时，请稍后重试";
                } else if (cause instanceof ConnectException) {
                    errorCode = "CONNECTION_ERROR";
                    errorMessage = "服务连接失败，请检查网络";
                } else if (cause instanceof feign.FeignException.Forbidden) {
                    errorCode = "FORBIDDEN_ERROR";
                    errorMessage = "访问被拒绝，权限不足";
                } else if (cause instanceof feign.FeignException.NotFound) {
                    errorCode = "NOT_FOUND_ERROR";
                    errorMessage = "请求的资源不存在";
                } else if (cause instanceof feign.FeignException.InternalServerError) {
                    errorCode = "SERVER_ERROR";
                    errorMessage = "服务器内部错误";
                }
                
                // 记录详细的异常日志
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG, 
                    "ExceptionClient.forbidden fallback triggered, errorCode={}, cause={}", 
                    errorCode, cause.getMessage(), cause);
                
                // 返回降级响应
                Meta meta = new Meta(false, errorCode, errorMessage);
                return new BaseResponse<>(meta, null);
            }
        };
    }


}
