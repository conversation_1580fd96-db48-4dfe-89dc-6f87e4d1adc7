package cn.com.chinastock.cnf.feign.examples.client.fallback;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.feign.examples.client.FallbackTestClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

@Component
public class FallbackTestClientFallbackFactory implements FallbackFactory<FallbackTestClient> {

    @Override
    public FallbackTestClient create(Throwable cause) {
        return new FallbackTestClient() {
            @Override
            public BaseResponse<String> testGet() {
                String errorCode = determineErrorCode(cause);
                String errorMessage = determineErrorMessage(cause);
                
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG, 
                    "FallbackTestClient.testGet fallback triggered, errorCode={}, cause={}", 
                    errorCode, cause.getMessage(), cause);
                
                Meta meta = new Meta(false, errorCode, errorMessage);
                return new BaseResponse<>(meta, "Fallback: 服务调用失败，返回默认响应");
            }

            @Override
            public BaseResponse<String> testGetWithParam(Long id) {
                String errorCode = determineErrorCode(cause);
                String errorMessage = determineErrorMessage(cause);
                
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG, 
                    "FallbackTestClient.testGetWithParam fallback triggered, id={}, errorCode={}, cause={}", 
                    id, errorCode, cause.getMessage(), cause);
                
                Meta meta = new Meta(false, errorCode, errorMessage);
                return new BaseResponse<>(meta, "Fallback: 无法获取ID=" + id + "的数据，返回默认值");
            }

            @Override
            public BaseResponse<String> testSlowRequest() {
                String errorCode = determineErrorCode(cause);
                String errorMessage = determineErrorMessage(cause);
                
                GalaxyLogger.error(LogCategory.EXCEPTION_LOG, 
                    "FallbackTestClient.testSlowRequest fallback triggered, errorCode={}, cause={}", 
                    errorCode, cause.getMessage(), cause);
                
                Meta meta = new Meta(false, errorCode, errorMessage);
                return new BaseResponse<>(meta, "Fallback: 请求超时，返回缓存数据或默认响应");
            }
        };
    }

    /**
     * 根据异常类型确定错误代码
     * @param cause 异常对象
     * @return 错误代码
     */
    private String determineErrorCode(Throwable cause) {
        if (cause instanceof SocketTimeoutException) {
            return "TIMEOUT_ERROR";
        } else if (cause instanceof ConnectException) {
            return "CONNECTION_ERROR";
        } else if (cause instanceof UnknownHostException) {
            return "HOST_NOT_FOUND";
        } else if (cause instanceof feign.FeignException.InternalServerError) {
            return "SERVER_ERROR";
        } else if (cause instanceof feign.FeignException.ServiceUnavailable) {
            return "SERVICE_UNAVAILABLE";
        } else {
            return "UNKNOWN_ERROR";
        }
    }

    /**
     * 根据异常类型确定错误消息
     * @param cause 异常对象
     * @return 错误消息
     */
    private String determineErrorMessage(Throwable cause) {
        if (cause instanceof SocketTimeoutException) {
            return "请求超时，请稍后重试";
        } else if (cause instanceof ConnectException) {
            return "无法连接到服务，请检查网络";
        } else if (cause instanceof UnknownHostException) {
            return "服务地址无法解析";
        } else if (cause instanceof feign.FeignException.InternalServerError) {
            return "服务器内部错误";
        } else if (cause instanceof feign.FeignException.ServiceUnavailable) {
            return "服务暂时不可用";
        } else {
            return "服务调用失败: " + cause.getMessage();
        }
    }
}
