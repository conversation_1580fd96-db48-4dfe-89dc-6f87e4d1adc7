package cn.com.chinastock.cnf.feign.examples.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.feign.examples.client.fallback.ExceptionClientFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "exception-service", fallbackFactory = ExceptionClientFallbackFactory.class)
public interface ExceptionClient {

    @GetMapping("/api/test/exception/forbidden")
    BaseResponse<Object> forbidden();
}
