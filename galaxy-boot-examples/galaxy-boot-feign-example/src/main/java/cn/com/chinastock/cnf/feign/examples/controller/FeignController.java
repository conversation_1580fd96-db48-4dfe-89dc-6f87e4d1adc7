package cn.com.chinastock.cnf.feign.examples.controller;

import cn.com.chinastock.cnf.core.exception.GalaxyFeignException;
import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.feign.examples.client.ExceptionClient;
import cn.com.chinastock.cnf.feign.examples.client.FallbackTestClient;
import cn.com.chinastock.cnf.feign.examples.client.LogClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test/feign")
public class FeignController {

    @Autowired
    private LogClient logClient;

    @Autowired
    private ExceptionClient exceptionClient;

    @Autowired
    private FallbackTestClient fallbackTestClient;

    @GetMapping("/log")
    public BaseResponse<Object> callLog() {
        String data = logClient.gzipResponse(10);
        return new BaseResponse<>(new Meta(true, "0", "success"), data);
    }

    @GetMapping("/not-found")
    public BaseResponse<Object> callNotFoundAPI() {
        try {
            logClient.notFound();
        } catch (GalaxyFeignException e) {
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    @GetMapping("/forbidden")
    public BaseResponse<Object> callException() {
        try {
            exceptionClient.forbidden();
        } catch (GalaxyFeignException e) {
            return new BaseResponse<>(e.getMeta(), null);
        }
        return new BaseResponse<>(new Meta(true, "0", "success"), null);
    }

    /**
     * 测试 LogClient 的 fallback 功能
     * 当服务不可用时，会触发 LogClientFallback 中的降级逻辑
     * @return 测试结果响应
     */
    @GetMapping("/fallback/log")
    public BaseResponse<Object> testLogFallback() {
        try {
            // 调用一个不存在的服务，触发 fallback
            logClient.logException();
            return new BaseResponse<>(new Meta(true, "0", "LogClient fallback 测试成功"), null);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "ERROR", "LogClient fallback 测试失败: " + e.getMessage()), null);
        }
    }

    /**
     * 测试 LogClient 的 multiLine 方法 fallback 功能
     * 当服务不可用时，会返回默认的 LogDTO 对象
     * @return 测试结果响应，包含 fallback 返回的 LogDTO 对象
     */
    @GetMapping("/fallback/log/multi-line")
    public BaseResponse<Object> testLogMultiLineFallback() {
        try {
            // 调用可能失败的服务，触发 fallback
            var result = logClient.multiLine();
            return new BaseResponse<>(new Meta(true, "0", "LogClient multiLine fallback 测试成功"), result);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "ERROR", "LogClient multiLine fallback 测试失败: " + e.getMessage()), null);
        }
    }

    /**
     * 测试 ExceptionClient 的 fallbackFactory 功能
     * 当服务不可用时，会触发 ExceptionClientFallbackFactory 中的降级逻辑
     * @return 测试结果响应，包含 fallbackFactory 返回的结果
     */
    @GetMapping("/fallback/exception")
    public BaseResponse<Object> testExceptionFallback() {
        try {
            // 调用可能失败的服务，触发 fallbackFactory
            BaseResponse<Object> result = exceptionClient.forbidden();
            return new BaseResponse<>(new Meta(true, "0", "ExceptionClient fallbackFactory 测试成功"), result);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "ERROR", "ExceptionClient fallbackFactory 测试失败: " + e.getMessage()), null);
        }
    }

    /**
     * 测试确定会触发 fallback 的场景
     * 调用不存在的服务，100% 触发连接异常
     * @return 测试结果响应，展示真实的 fallback 行为
     */
    @GetMapping("/fallback/guaranteed")
    public BaseResponse<Object> testGuaranteedFallback() {
        try {
            // 这个调用一定会失败，因为指向不存在的服务
            BaseResponse<String> result = fallbackTestClient.testGet();
            return new BaseResponse<>(new Meta(true, "0", "Fallback 测试成功"), result);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "UNEXPECTED_ERROR", "意外错误，fallback 可能未正确配置: " + e.getMessage()), null);
        }
    }

    /**
     * 测试带参数的 fallback
     * @return 测试结果响应
     */
    @GetMapping("/fallback/guaranteed-with-param")
    public BaseResponse<Object> testGuaranteedFallbackWithParam() {
        try {
            BaseResponse<String> result = fallbackTestClient.testGetWithParam(12345L);
            return new BaseResponse<>(new Meta(true, "0", "带参数的 Fallback 测试成功"), result);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "UNEXPECTED_ERROR", "意外错误: " + e.getMessage()), null);
        }
    }

    /**
     * 测试慢请求的 fallback
     * @return 测试结果响应
     */
    @GetMapping("/fallback/guaranteed-slow")
    public BaseResponse<Object> testGuaranteedSlowFallback() {
        try {
            BaseResponse<String> result = fallbackTestClient.testSlowRequest();
            return new BaseResponse<>(new Meta(true, "0", "慢请求 Fallback 测试成功"), result);
        } catch (Exception e) {
            return new BaseResponse<>(new Meta(false, "UNEXPECTED_ERROR", "意外错误: " + e.getMessage()), null);
        }
    }
}
