app:
  id: datasource
apollo:
  meta: http://localhost:8080
server:
  port: 8088

spring:
  application:
    name: galaxy-boot-database-example-service
  jpa:
    database: mysql
    show-sql: false
    hibernate:
      ddl-auto: update
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: sun
    password: f5GGWuGwy,!f.s#5+n5
    hikari:
      maximum-pool-size: 10

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region
  prometheus:
    metrics:
      export:
        enabled: true

galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
    dynamic-refresh-username-password: true # 动态修改数据库密码

  metrics:
    datasource:
      prometheus:
        enabled: true

  system:
    code: DXP

  log:
    request-response:
      enabled: false
      request-headers: false
    performance:
      enabled: false
    default-category: BUSINESS_LOG
    exception-pretty-print: true
