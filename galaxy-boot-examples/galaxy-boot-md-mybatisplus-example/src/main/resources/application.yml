server:
  port: 8083

spring:
  application:
    name: galaxy-boot-md-mybatisplus-example

  galaxy-datasource:
    order:
      primary: true
      type: mybatis-plus  # 指定使用MyBatisPlus
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.entity
        mapper: cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.mapper
      datasource:
        url: jdbc:h2:mem:orderdb;DB_CLOSE_DELAY=-1;MODE=MySQL
        username: sa
        password:
      # Hikari 连接池配置
      hikari:
        pool-name: OrderPool
        maximum-pool-size: 12
        minimum-idle: 3
        connection-timeout: 25s
        idle-timeout: 8m
        max-lifetime: 20m
        connection-test-query: SELECT 1
      mybatis-plus:
        mapper-locations: classpath:mapper/order/*.xml
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.entity
        configuration:
          map-underscore-to-camel-case: true
          cache-enabled: false
          safe-row-bounds-enabled: true
          safe-result-handler-enabled: false
        global-config:
          db-config:
            logic-delete-field: deleted
            logic-delete-value: 1
            logic-not-delete-value: 0

galaxy:
  system:
    code: CNF

  metrics:
    datasource:
      prometheus:
        enabled: false

  log:
    request-response:
      enabled: true
      request-headers: true
      response-headers: true
      mask-field: true
    performance:
      enabled: true
    default-category: APP_LOG
    exception-pretty-print: true

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: false

logging:
  level:
    cn.com.chinastock.cnf.mdatasource: DEBUG
    cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.mapper: DEBUG

apollo:
  bootstrap:
    enabled: false