package cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.dto.CreateOrderRequest;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.dto.UpdateOrderRequest;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.entity.Order;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @GetMapping
    public ResponseEntity<List<Order>> getAllOrders() {
        try {
            List<Order> orders = orderService.getAllOrders();
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to get all orders", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Order> getOrderById(@PathVariable Long id) {
        try {
            Order order = orderService.getOrderById(id);
            return order != null ? ResponseEntity.ok(order) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find order by ID: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<Order> findOrderByOrderNo(@RequestParam("order_no") String orderNo) {
        try {
            Order order = orderService.findByOrderNo(orderNo);
            return order != null ? ResponseEntity.ok(order) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find order by orderNo: {}", orderNo, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping
    public ResponseEntity<Order> createOrder(@RequestBody CreateOrderRequest request) {
        try {
            Order order = orderService.createOrder(request.getOrderNo(), request.getCustomerName(), request.getTotalAmount());
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to create order", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Order> updateOrder(@PathVariable Long id, @RequestBody UpdateOrderRequest request) {
        try {
            Order updatedOrder = orderService.updateOrder(id, request.getOrderNo(), request.getCustomerName(), request.getTotalAmount());
            return updatedOrder != null ? ResponseEntity.ok(updatedOrder) : ResponseEntity.notFound().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to update order: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteOrder(@PathVariable Long id) {
        try {
            orderService.deleteOrder(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to delete order: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/mybatis-plus-transaction")
    public ResponseEntity<Map<String, Object>> testMybatisPlusTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            orderService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("MyBatisPlus transaction failed as expected: {}", e.getMessage());
            boolean rollbackSuccess = orderService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess ? "MyBatisPlus transaction rollback successful" : "MyBatisPlus transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }

}
