package cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.mapper;

import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.entity.Order;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrderMapper extends BaseMapper<Order> {

    @Select("SELECT * FROM `orders` WHERE order_no = #{orderNo} AND deleted = 0")
    Order findByOrderNo(@Param("orderNo") String orderNo);

    default List<Order> findByStatus(String status) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectList(queryWrapper);
    }

    default long countOrders() {
        return selectCount(null);
    }

    default long countByStatus(String status) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return selectCount(queryWrapper);
    }
}
