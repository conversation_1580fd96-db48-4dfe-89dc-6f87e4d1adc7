package cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.entity.Order;
import cn.com.chinastock.cnf.mdatasource.mybatisplus.examples.domain.order.mapper.OrderMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class OrderService extends ServiceImpl<OrderMapper, Order> {

    @Autowired
    @Qualifier("orderDataSource")
    private DataSource orderDataSource;

    @PostConstruct
    public void initializeData() {
        initializeDatabase();
        initTestData();
    }

    public void initializeDatabase() {
        try {
            GalaxyLogger.info("----> Initializing Order database schema...");
            ScriptUtils.executeSqlScript(orderDataSource.getConnection(),
                    new ClassPathResource("sql/order-schema.sql"));
            GalaxyLogger.info("----> Order database schema initialized successfully.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Failed to initialize Order database schema: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize Order database", e);
        }
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public Order createOrder(BigDecimal amount, String status) {
        Order order = new Order();
        order.setOrderNo("ORD-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        order.setAmount(amount);
        order.setStatus(status);
        order.setDeleted(0);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        save(order);
        return order;
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public Order createOrder(String orderNo, String customerName, BigDecimal totalAmount) {
        Order order = new Order();
        order.setOrderNo(orderNo != null ? orderNo : "ORD-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        order.setCustomerName(customerName);
        order.setAmount(totalAmount);
        order.setStatus("PENDING");
        order.setDeleted(0);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());

        save(order);
        return order;
    }

    public Order getOrderById(Long id) {
        return getById(id);
    }

    public Order findByOrderNo(String orderNo) {
        return baseMapper.findByOrderNo(orderNo);
    }

    public List<Order> findAll() {
        return baseMapper.selectList(null);
    }

    public List<Order> findByStatus(String status) {
        return baseMapper.findByStatus(status);
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public Order updateOrder(Long id, String orderNo, String customerName, BigDecimal totalAmount) {
        Order order = getById(id);
        if (order != null) {
            if (orderNo != null) {
                order.setOrderNo(orderNo);
            }
            if (customerName != null) {
                order.setCustomerName(customerName);
            }
            if (totalAmount != null) {
                order.setAmount(totalAmount);
            }
            order.setUpdateTime(LocalDateTime.now());
            updateById(order);
            return order;
        }
        return null;
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public void updateStatus(Long id, String status) {
        Order order = getById(id);
        if (order != null) {
            order.setStatus(status);
            order.setUpdateTime(LocalDateTime.now());
            updateById(order);
        }
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public void deleteOrder(Long id) {
        removeById(id);
    }

    public long countByStatus(String status) {
        return baseMapper.countByStatus(status);
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public void initTestData() {
        // 清空现有数据
        baseMapper.delete(null);

        // 创建测试订单
        createOrder(new BigDecimal("100.00"), "PENDING");
        createOrder(new BigDecimal("200.50"), "PAID");
        createOrder(new BigDecimal("150.75"), "SHIPPED");
        createOrder(new BigDecimal("300.25"), "COMPLETED");
    }

    @Transactional(transactionManager = "orderTransactionManager")
    public void testTransactionRollback() {
        // 创建一个订单
        createOrder(new BigDecimal("999.99"), "TEST");

        // 抛出异常触发回滚
        throw new RuntimeException("测试MyBatisPlus事务回滚");
    }

    public boolean verifyTransactionRollback() {
        long testOrderCount = countByStatus("TEST");
        if (testOrderCount > 0) {
            GalaxyLogger.error("!!!!!! [FAILURE] MyBatisPlus transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] MyBatisPlus transaction rolled back successfully.");
            return true;
        }
    }

    public List<Order> getAllOrders() {
        List<Order> orders = findAll();
        orders.forEach(o ->
                GalaxyLogger.info("[MyBatisPlus Order] Found Order: ID={}, OrderNo={}, Amount={}, Status={}",
                        o.getId(), o.getOrderNo(), o.getAmount(), o.getStatus())
        );
        return orders;
    }
}
