package cn.com.chinastock.cnf.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.examples.dto.BatchMessageResponse;
import cn.com.chinastock.cnf.examples.dto.MessageRequest;
import cn.com.chinastock.cnf.examples.dto.MessageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 线程池控制器 - 用于测试多线程环境下的链路追踪
 *
 * <AUTHOR> Boot Team
 */
@RestController
@RequestMapping("/api/test/threadpool")
public class ThreadPoolController {

    private final ExecutorService executorService;

    public ThreadPoolController(ExecutorService executorService) {
        this.executorService = executorService;
    }

    /**
     * 批量处理消息API
     *
     * @param request 消息处理请求
     * @return 处理结果
     */
    @PostMapping("/process-messages")
    public ResponseEntity<BatchMessageResponse> processMessages(@RequestBody MessageRequest request) {
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "开始批量处理消息, 消息数量: {}, 消息延迟: {}ms",
                request.getMessages() != null ? request.getMessages().size() : 0,
                request.getDelayMs());

        List<CompletableFuture<MessageResult>> futures = new ArrayList<>();

        // 为每个消息创建异步任务
        for (String message : request.getMessages()) {
            CompletableFuture<MessageResult> future = CompletableFuture.supplyAsync(() -> {
                // 在子线程中处理消息
                return processMessageWithTracing(message, request.getDelayMs());
            }, executorService);

            futures.add(future);
        }

        return ResponseEntity.ok(getBatchMessageResponse(request, futures));
    }

    /**
     * 在子线程中处理消息，确保TraceContext正确传播
     *
     * @param message 消息内容
     * @param delayMs 处理延迟时间
     * @return 消息处理结果
     */
    private MessageResult processMessageWithTracing(String message, Long delayMs) {
        // 创建子Span用于追踪
        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "开始处理消息: {}", message);

        try {
            // 模拟处理时间
            if (delayMs != null && delayMs > 0) {
                Thread.sleep(delayMs);
            }

            // 模拟消息处理逻辑
            String processedMessage = "PROCESSED_" + message.toUpperCase() + "_" + System.currentTimeMillis();
            GalaxyLogger.info(LogCategory.BUSINESS_LOG, "消息处理完成: {} -> {}", message, processedMessage);
            return new MessageResult(message, processedMessage, "SUCCESS");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "消息处理被中断: {}", message);
            return new MessageResult(message, null, "INTERRUPTED");
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "消息处理失败: {}", message);
            return new MessageResult(message, null, "FAILED");
        }
    }

    private static BatchMessageResponse getBatchMessageResponse(MessageRequest request, List<CompletableFuture<MessageResult>> futures) {
        // 等待所有任务完成
        List<MessageResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 等待所有任务完成，设置超时时间
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 等待最多5秒
            allFutures.get(5, TimeUnit.SECONDS);

            // 收集结果
            for (CompletableFuture<MessageResult> future : futures) {
                try {
                    MessageResult result = future.get();
                    results.add(result);

                    if ("SUCCESS".equals(result.getStatus())) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "获取任务结果失败", e);
                    failureCount++;
                }
            }

        } catch (TimeoutException e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "批量处理超时", e);
            failureCount = request.getMessages().size() - successCount;
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "批量处理异常", e);
            failureCount = request.getMessages().size() - successCount;
        }


        GalaxyLogger.info(LogCategory.BUSINESS_LOG, "批量处理完成, 总数: {}, 成功: {}, 失败: {}",
                request.getMessages().size(), successCount, failureCount);

        BatchMessageResponse response = new BatchMessageResponse(
                request.getMessages().size(),
                successCount,
                failureCount,
                results
        );
        return response;
    }

}
