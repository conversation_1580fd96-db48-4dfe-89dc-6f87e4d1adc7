package cn.com.chinastock.cnf.examples.config;

import io.micrometer.context.ContextExecutorService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 线程池配置类 - 简单示例，展示如何配置支持链路追踪的线程池
 *
 * <AUTHOR> Boot Team
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 消息处理线程池 - 用于演示多线程环境下的链路追踪
     *
     * @return 支持链路追踪的线程池
     */
    @Bean(name = "messageProcessorExecutor")
    public ExecutorService executorService() {
        return ContextExecutorService.wrap(Executors.newFixedThreadPool(10));
    }
}
