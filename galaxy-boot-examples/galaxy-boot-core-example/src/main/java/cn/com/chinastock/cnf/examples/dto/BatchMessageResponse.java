package cn.com.chinastock.cnf.examples.dto;

import java.util.List;

/**
 * 批量消息处理响应DTO
 *
 * <AUTHOR> Boot Team
 */
public class BatchMessageResponse {
    
    /**
     * 总消息数量
     */
    private Integer totalCount;
    
    /**
     * 成功处理数量
     */
    private Integer successCount;
    
    /**
     * 失败处理数量
     */
    private Integer failureCount;

    /**
     * 处理结果列表
     */
    private List<MessageResult> results;

    public BatchMessageResponse() {
    }

    public BatchMessageResponse(Integer totalCount, Integer successCount, Integer failureCount,
                                List<MessageResult> results) {
        this.totalCount = totalCount;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.results = results;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Integer failureCount) {
        this.failureCount = failureCount;
    }

    public List<MessageResult> getResults() {
        return results;
    }

    public void setResults(List<MessageResult> results) {
        this.results = results;
    }

    @Override
    public String toString() {
        return "BatchMessageResponse{" +
                "totalCount=" + totalCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", results=" + results +
                '}';
    }
}
