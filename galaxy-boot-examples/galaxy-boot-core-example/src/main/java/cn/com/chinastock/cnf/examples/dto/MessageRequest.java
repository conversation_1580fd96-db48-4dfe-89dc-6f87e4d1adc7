package cn.com.chinastock.cnf.examples.dto;

import java.util.List;

/**
 * 批量消息处理请求DTO
 *
 * <AUTHOR> Boot Team
 */
public class MessageRequest {
    
    /**
     * 消息列表
     */
    private List<String> messages;
    
    /**
     * 处理延迟时间（毫秒），用于模拟处理时间
     */
    private Long delayMs = 1000L;

    public MessageRequest() {
    }

    public MessageRequest(List<String> messages, Long delayMs) {
        this.messages = messages;
        this.delayMs = delayMs;
    }

    public List<String> getMessages() {
        return messages;
    }

    public void setMessages(List<String> messages) {
        this.messages = messages;
    }

    public Long getDelayMs() {
        return delayMs;
    }

    public void setDelayMs(Long delayMs) {
        this.delayMs = delayMs;
    }

    @Override
    public String toString() {
        return "MessageRequest{" +
                "messages=" + messages +
                ", delayMs=" + delayMs +
                '}';
    }
}
