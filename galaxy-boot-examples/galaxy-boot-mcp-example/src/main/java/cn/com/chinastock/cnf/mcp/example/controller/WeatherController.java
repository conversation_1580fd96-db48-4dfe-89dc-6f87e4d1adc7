package cn.com.chinastock.cnf.mcp.example.controller;

import cn.com.chinastock.cnf.mcp.example.model.WeatherForecast;
import cn.com.chinastock.cnf.mcp.example.model.WeatherInfo;
import cn.com.chinastock.cnf.mcp.example.service.WeatherService;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 天气控制器
 * <p>
 * 演示如何使用 Spring AI @Tool 注解将 REST API 注册为 MCP Tool
 * </p>
 *
 * <AUTHOR> Boot Team
 */
@RestController
@RequestMapping("/api/weather")
public class WeatherController {

    @Autowired
    private WeatherService weatherService;

    /**
     * 获取指定城市的当前天气信息
     *
     * @param city 城市名称
     * @return 天气信息
     */
    @GetMapping("/current")
    @Tool(
            name = "getCurrentWeather",
            description = "获取指定城市的当前天气信息，包括温度、湿度、风速、气压等详细信息"
    )
    public ResponseEntity<WeatherInfo> getCurrentWeather(@RequestParam String city) {
        try {
            WeatherInfo weather = weatherService.getWeather(city);
            return ResponseEntity.ok(weather);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取指定城市的天气预报
     *
     * @param city 城市名称
     * @param days 预报天数（1-7天）
     * @return 天气预报列表
     */
    @GetMapping("/forecast")
    @Tool(
            name = "getWeatherForecast",
            description = "获取指定城市的天气预报，支持1-7天的预报"
    )
    public ResponseEntity<List<WeatherForecast>> getWeatherForecast(
            @RequestParam String city,
            @RequestParam(defaultValue = "3") int days) {
        try {
            List<WeatherForecast> forecasts = weatherService.getForecast(city, days);
            return ResponseEntity.ok(forecasts);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取支持的城市列表
     *
     * @return 支持的城市列表
     */
    @GetMapping("/cities")
    @Tool(
            name = "getSupportedCities",
            description = "获取天气服务支持的所有城市列表"
    )
    public ResponseEntity<List<String>> getSupportedCities() {
        List<String> cities = weatherService.getSupportedCities();
        return ResponseEntity.ok(cities);
    }

    /**
     * 比较多个城市的天气
     *
     * @param cities 城市列表，用逗号分隔
     * @return 城市天气比较结果
     */
    @GetMapping("/compare")
    @Tool(
            name = "compareWeather",
            description = "比较多个城市的当前天气情况，便于选择出行目的地"
    )
    public ResponseEntity<Map<String, WeatherInfo>> compareWeather(@RequestParam String cities) {
        try {
            String[] cityArray = cities.split(",");
            Map<String, WeatherInfo> weatherMap = new java.util.HashMap<>();

            for (String city : cityArray) {
                city = city.trim();
                if (!city.isEmpty()) {
                    WeatherInfo weather = weatherService.getWeather(city);
                    weatherMap.put(city, weather);
                }
            }

            return ResponseEntity.ok(weatherMap);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取天气建议
     *
     * @param city 城市名称
     * @return 基于天气的出行建议
     */
    @GetMapping("/advice")
    @Tool(
            name = "getWeatherAdvice",
            description = "根据指定城市的天气情况提供出行和穿衣建议"
    )
    public ResponseEntity<Map<String, String>> getWeatherAdvice(@RequestParam String city) {
        try {
            WeatherInfo weather = weatherService.getWeather(city);
            Map<String, String> advice = generateAdvice(weather);
            return ResponseEntity.ok(advice);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据天气信息生成建议
     *
     * @param weather 天气信息
     * @return 包含各种建议的映射
     */
    private Map<String, String> generateAdvice(WeatherInfo weather) {
        Map<String, String> advice = new java.util.HashMap<>();

        // 穿衣建议
        String clothingAdvice = generateClothingAdvice(weather.getTemperature(), weather.getDescription());
        advice.put("clothing", clothingAdvice);

        // 出行建议
        String travelAdvice = generateTravelAdvice(weather.getDescription(), weather.getWindSpeed());
        advice.put("travel", travelAdvice);

        // 健康建议
        String healthAdvice = generateHealthAdvice(weather.getHumidity(), weather.getDescription());
        advice.put("health", healthAdvice);

        // 活动建议
        String activityAdvice = generateActivityAdvice(weather.getDescription(), weather.getTemperature());
        advice.put("activity", activityAdvice);

        return advice;
    }

    private String generateClothingAdvice(double temperature, String description) {
        if (temperature > 25) {
            return "建议穿着轻薄的夏装，如短袖、短裤等";
        } else if (temperature > 15) {
            return "建议穿着春秋装，如长袖衬衫、薄外套等";
        } else if (temperature > 5) {
            return "建议穿着厚外套、毛衣等保暖衣物";
        } else {
            return "建议穿着羽绒服、厚毛衣等厚重保暖衣物";
        }
    }

    private String generateTravelAdvice(String description, double windSpeed) {
        if (description.contains("雨")) {
            return "有降雨，建议携带雨具，注意交通安全";
        } else if (description.contains("雾") || description.contains("霾")) {
            return "能见度较低，驾车请减速慢行，注意安全";
        } else if (windSpeed > 10) {
            return "风力较大，外出请注意防风，避免高空作业";
        } else {
            return "天气适宜出行，注意防晒和补水";
        }
    }

    private String generateHealthAdvice(int humidity, String description) {
        if (humidity > 70) {
            return "湿度较高，注意防潮，适当使用除湿设备";
        } else if (humidity < 30) {
            return "空气干燥，注意补水，可使用加湿器";
        } else if (description.contains("霾")) {
            return "空气质量较差，建议减少户外活动，外出佩戴口罩";
        } else {
            return "空气湿度适宜，适合户外活动";
        }
    }

    private String generateActivityAdvice(String description, double temperature) {
        if (description.contains("雨")) {
            return "雨天适合室内活动，如阅读、看电影等";
        } else if (temperature > 30) {
            return "高温天气，建议选择室内运动或早晚户外活动";
        } else if (temperature < 0) {
            return "低温天气，建议选择室内运动，注意保暖";
        } else {
            return "天气适宜，可以进行各种户外活动";
        }
    }
}
