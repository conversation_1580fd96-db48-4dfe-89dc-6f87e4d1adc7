package cn.com.chinastock.cnf.mcp.example.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

/**
 * 天气预报模型
 *
 * <AUTHOR> Boot Team
 */
public class WeatherForecast {

    @JsonProperty("date")
    private LocalDate date;

    @JsonProperty("city")
    private String city;

    @JsonProperty("max_temperature")
    private double maxTemperature;

    @JsonProperty("min_temperature")
    private double minTemperature;

    @JsonProperty("humidity")
    private int humidity;

    @JsonProperty("description")
    private String description;

    @JsonProperty("wind_speed")
    private double windSpeed;

    @JsonProperty("precipitation_probability")
    private int precipitationProbability;

    public WeatherForecast() {
    }

    public WeatherForecast(LocalDate date, String city, double maxTemperature, double minTemperature, 
                          int humidity, String description, double windSpeed, int precipitationProbability) {
        this.date = date;
        this.city = city;
        this.maxTemperature = maxTemperature;
        this.minTemperature = minTemperature;
        this.humidity = humidity;
        this.description = description;
        this.windSpeed = windSpeed;
        this.precipitationProbability = precipitationProbability;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public double getMaxTemperature() {
        return maxTemperature;
    }

    public void setMaxTemperature(double maxTemperature) {
        this.maxTemperature = maxTemperature;
    }

    public double getMinTemperature() {
        return minTemperature;
    }

    public void setMinTemperature(double minTemperature) {
        this.minTemperature = minTemperature;
    }

    public int getHumidity() {
        return humidity;
    }

    public void setHumidity(int humidity) {
        this.humidity = humidity;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public int getPrecipitationProbability() {
        return precipitationProbability;
    }

    public void setPrecipitationProbability(int precipitationProbability) {
        this.precipitationProbability = precipitationProbability;
    }

    @Override
    public String toString() {
        return "WeatherForecast{" +
                "date=" + date +
                ", city='" + city + '\'' +
                ", maxTemperature=" + maxTemperature +
                ", minTemperature=" + minTemperature +
                ", humidity=" + humidity +
                ", description='" + description + '\'' +
                ", windSpeed=" + windSpeed +
                ", precipitationProbability=" + precipitationProbability +
                '}';
    }
}
