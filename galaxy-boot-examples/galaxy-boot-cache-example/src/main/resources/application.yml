server:
  port: 8085

spring:
  # Spring Boot 原生的 Caffeine 默认配置
  cache:
    type: caffeine
    caffeine:
      # 这是一个全局的、默认的缓存配置
      # 所有未被特殊指定的配置项都将从此继承
      spec: > # 使用 spec 字符串进行详细配置，非常灵活
        maximumSize=500,
        expireAfterWrite=5s,
        recordStats
  # 自定义的、按缓存名区分的特殊配置
  galaxy-cache:
    specs:
      # 'longTermCache' 的特殊配置
      # 它会覆盖默认的 expireAfterWrite，并添加 initialCapacity
      # 但它会继承默认的 maximumSize 和 recordStats
      longTermCache:
        spec: initialCapacity=200,expireAfterWrite=10m

      # 'shortTermCache' 的特殊配置
      # 它只覆盖 maximumSize，其他配置全部继承自默认值
      shortTermCache:
        spec: maximumSize=500,expireAfterWrite=30s

galaxy:
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      request-headers: true
      response-headers: true
      mask-field: false
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

logging:
  level:
    com.ctrip.framework: OFF
