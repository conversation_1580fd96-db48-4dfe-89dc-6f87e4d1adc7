package cn.com.chinastock.cnf.webclient.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webclient.examples.client.LoadBalancerClient;
import cn.com.chinastock.cnf.webclient.examples.client.LogClient;
import cn.com.chinastock.cnf.webclient.exception.GalaxyWebClientException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/test/webclient")
public class WebClientController {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    @Autowired
    private LogClient logClient;

    @Autowired
    private LoadBalancerClient loadBalancerClient;

    @GetMapping("/log-exception")
    public Mono<BaseResponse<String>> callLogException() {
        logger.info("callLogException");
        return logClient.logException()
                .doOnSuccess(result -> logger.info("LogClient call successful: {}", result))
                .then(Mono.fromSupplier(() -> {
                    BaseResponse<String> response = new BaseResponse<>(new Meta(true, "0", "success"), "Log exception called");
                    logger.info("Returning success response: {}", response);
                    return response;
                }))
                .onErrorResume(throwable -> {
                    logger.error("LogClient call failed", throwable);
                    BaseResponse<String> errorResponse;
                    if (throwable instanceof GalaxyWebClientException e) {
                        errorResponse = new BaseResponse<>(e.getMeta(), "Log exception succeed");
                    } else {
                        // 返回错误信息而不是重新抛出异常
                        errorResponse = new BaseResponse<>(
                            new Meta(false, "ERROR", "WebClient call failed: " + throwable.getMessage()),
                            "Error occurred"
                        );
                    }
                    logger.info("Returning error response: {}", errorResponse);
                    return Mono.just(errorResponse);
                });
    }

    @GetMapping("/simple-error")
    public Mono<BaseResponse<String>> simpleError() {
        logger.info("simpleError");
        // 直接返回一个错误响应，不涉及WebClient调用
        return Mono.just(new BaseResponse<>(
            new Meta(false, "ERROR", "Simple error test"),
            "Simple error response"
        ));
    }

    @GetMapping("/not-found")
    public Mono callNotFoundAPI() {
        logger.info("callLogException");

        return logClient.notFound();
    }

    @GetMapping("/multi-line")
    public Flux callMultiLine() {
        logger.info("callLogException");

        return logClient.multiLine();
    }

    @GetMapping("/esb")
    public Mono callESB() {
        logger.info("callESB");

        return logClient.esbCheck("1234567890");
    }

    @GetMapping("/echo")
    public Mono echo(@RequestParam String input) {
        return Mono.just("echo: " + input);
    }

    @GetMapping("/echo-body")
    public Mono echoBody(@RequestBody Mono<String> input) {
        logger.info("echoBody");
        return input.map(s -> "echo: " + s);
    }

    @GetMapping("/loadbalancer")
    public Mono<BaseResponse<Object>> loadbalancer() {
        logger.info("loadbalancer");
        return loadBalancerClient.headers("YH0019010400001")
                .map(response -> new BaseResponse<Object>(new Meta(true, "0", "success"), response))
                .onErrorResume(GalaxyWebClientException.class, e -> {
                    logger.error("GalaxyWebClientException, code={}, message={}", e.getCode(), e.getMessage());
                    return Mono.just(new BaseResponse<Object>(e.getMeta(), null));
                });
    }

    @PostMapping("/empty-response")
    public Mono<Void> emptyResponse() {
        logger.info("Processing empty response request");
        // 模拟一些处理逻辑，但不返回任何响应体
        return Mono.fromRunnable(() -> {
            logger.info("Processing completed, no response body");
        });
    }
}