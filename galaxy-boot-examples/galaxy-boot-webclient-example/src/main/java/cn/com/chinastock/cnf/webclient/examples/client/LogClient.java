package cn.com.chinastock.cnf.webclient.examples.client;

import cn.com.chinastock.cnf.webclient.WebClientExchange;
import cn.com.chinastock.cnf.webclient.examples.dto.LogDTO;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@WebClientExchange(name = "log-service", url = "http://localhost:8089")
public interface LogClient {

    @GetExchange("/api/test/log/exception")
    Mono<Void> logException();

    @GetExchange("/api/test/log/not-found")
    Mono<Void> notFound();

    @GetExchange("/api/test/log/multi-line")
    Flux<LogDTO> multiLine();

    @GetExchange(value = "/api/test/log/esb-check")
    Mono<String> esbCheck(@RequestHeader("Function-No") String functionNo);

}