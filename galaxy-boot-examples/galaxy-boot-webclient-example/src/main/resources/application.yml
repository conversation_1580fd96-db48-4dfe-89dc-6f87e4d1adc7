server:
  port: 8088

spring:
  application:
    name: galaxy-boot-webclient-example-service
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            loadbalancer-service:
              - uri: http://localhost:8089

galaxy:
  webclient:
    esb:
      user: user
      password: password

  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: false
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

logging:
  level:
    cn.com.chinastock.cnf.webclient.filter: INFO

apollo:
  bootstrap:
    enabled: false