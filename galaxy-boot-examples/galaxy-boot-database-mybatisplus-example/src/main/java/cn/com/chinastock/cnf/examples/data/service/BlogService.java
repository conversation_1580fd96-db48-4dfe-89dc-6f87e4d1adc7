package cn.com.chinastock.cnf.examples.data.service;

import cn.com.chinastock.cnf.examples.data.entity.Blog;
import cn.com.chinastock.cnf.examples.data.mapper.BlogMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 博客业务服务类 - MyBatisPlus示例
 * 继承ServiceImpl后自动拥有基础的业务方法
 *
 * <AUTHOR> Boot Team
 */
@Service
public class BlogService extends ServiceImpl<BlogMapper, Blog> {

    /**
     * 创建博客
     *
     * @param title 标题
     * @param content 内容
     * @param author 作者
     * @return 创建的博客
     */
    @Transactional
    public Blog createBlog(String title, String content, String author) {
        Blog blog = new Blog(title, content, author);
        save(blog);  // 使用MyBatisPlus的save方法
        return blog;
    }

    /**
     * 根据ID获取博客
     *
     * @param id 博客ID
     * @return 博客信息
     */
    public Blog getBlogById(Long id) {
        return getById(id);  // 使用MyBatisPlus的getById方法
    }

    /**
     * 获取所有博客
     *
     * @return 所有博客列表
     */
    public List<Blog> getAllBlogs() {
        return list();  // 使用MyBatisPlus的list方法
    }

    /**
     * 根据标题关键字查找博客
     *
     * @param title 标题关键字
     * @return 博客列表
     */
    public List<Blog> findByTitleContaining(String title) {
        return baseMapper.findByTitleContaining(title);  // 使用自定义查询
    }

    /**
     * 根据作者查找博客
     *
     * @param author 作者
     * @return 博客列表
     */
    public List<Blog> findByAuthor(String author) {
        QueryWrapper<Blog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("author", author);
        return list(queryWrapper);  // 使用MyBatisPlus的条件查询
    }

    /**
     * 更新博客
     *
     * @param blog 博客信息
     * @return 是否更新成功
     */
    @Transactional
    public boolean updateBlog(Blog blog) {
        return updateById(blog);  // 使用MyBatisPlus的updateById方法
    }

    /**
     * 删除博客（逻辑删除）
     *
     * @param id 博客ID
     * @return 是否删除成功
     */
    @Transactional
    public boolean deleteBlog(Long id) {
        return removeById(id);  // 使用MyBatisPlus的removeById方法（支持逻辑删除）
    }

    /**
     * 根据作者统计博客数量
     *
     * @param author 作者
     * @return 博客数量
     */
    public long countByAuthor(String author) {
        return baseMapper.countByAuthor(author);  // 使用自定义查询
    }

    /**
     * 统计总博客数量
     *
     * @return 总博客数量
     */
    public long countAllBlogs() {
        return count();  // 使用MyBatisPlus的count方法
    }

    /**
     * 统计各作者的博客数量
     *
     * @return 作者博客数量统计
     */
    public List<Object> countBlogsGroupByAuthor() {
        return baseMapper.countBlogsGroupByAuthor();  // 使用自定义查询
    }
}
