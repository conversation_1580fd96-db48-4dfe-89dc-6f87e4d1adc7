app:
  id: datasource
apollo:
  meta: http://localhost:8080
server:
  port: 8089

spring:
  application:
    name: galaxy-boot-database-mybatisplus-example-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: sun
    password: f5GGWuGwy,!f.s#5+n5

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: prometheus
  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region
  prometheus:
    metrics:
      export:
        enabled: true

# MyBatisPlus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.com.chinastock.cnf.examples.data.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      table-prefix: ""

galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
    dynamic-refresh-username-password: true # 动态修改数据库密码（未来支持）

  metrics:
    datasource:
      prometheus:
        enabled: false

  system:
    code: DXP

  log:
    request-response:
      enabled: true
      request-headers: false
    performance:
      enabled: true
    default-category: BUSINESS_LOG
    exception-pretty-print: true
