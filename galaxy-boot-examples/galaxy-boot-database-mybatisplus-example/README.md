# Galaxy Boot Database MyBatisPlus Example

这个示例展示了如何使用 `galaxy-boot-starter-database-oceanbase` + MyBatisPlus 作为 ORM 框架来开发应用程序。

## 项目特性

- 使用 Galaxy Boot OceanBase Starter 进行数据库连接管理
- 使用 MyBatisPlus 作为 ORM 框架，提供强大的 CRUD 功能
- 继承 BaseMapper 和 ServiceImpl，自动获得基础 CRUD 方法
- 支持自动填充时间字段
- 支持逻辑删除
- 支持分页查询
- 集成 Spring Boot Actuator 监控
- 支持 Apollo 配置中心动态配置

## 技术栈

- Spring Boot 3.x
- MyBatisPlus 3.5.9
- Galaxy Boot OceanBase Starter
- HikariCP 连接池
- MySQL/OceanBase 数据库

## 项目结构

```
src/main/java/
├── config/
│   └── MybatisPlusConfig.java          # MyBatisPlus配置类
├── controller/
│   └── BlogController.java            # REST控制器
├── entity/
│   └── Blog.java                       # 实体类（使用MyBatisPlus注解）
├── mapper/
│   └── BlogMapper.java                 # Mapper接口（继承BaseMapper）
├── service/
│   └── BlogService.java                # 业务服务类（继承ServiceImpl）
└── MybatisPlusDatabaseExampleApplication.java  # 主启动类

src/main/resources/
├── application.yml                     # 应用配置文件
└── mybatis-config.xml                  # MyBatis配置文件
```

## 核心功能

### 1. 实体类注解

```java
@TableName("blog")                      // 指定表名
@TableId(type = IdType.ASSIGN_ID)       // 主键策略：雪花算法
@TableField(fill = FieldFill.INSERT)    // 插入时自动填充
@TableLogic                             // 逻辑删除字段
```

### 2. Mapper接口

继承 `BaseMapper<T>` 后自动拥有基础的 CRUD 方法：
- `insert(T entity)` - 插入记录
- `deleteById(Serializable id)` - 根据ID删除（逻辑删除）
- `updateById(T entity)` - 根据ID更新
- `selectById(Serializable id)` - 根据ID查询
- `selectList(Wrapper<T> queryWrapper)` - 条件查询
- `selectCount(Wrapper<T> queryWrapper)` - 统计查询

### 3. 服务类

继承 `ServiceImpl<M, T>` 后自动拥有基础的业务方法：
- `save(T entity)` - 保存实体
- `removeById(Serializable id)` - 根据ID删除（逻辑删除）
- `updateById(T entity)` - 根据ID更新
- `getById(Serializable id)` - 根据ID获取
- `list()` - 获取所有记录
- `list(Wrapper<T> queryWrapper)` - 条件查询
- `count()` - 统计总数

## 配置说明

### MyBatisPlus配置

```yaml
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.com.chinastock.cnf.examples.data.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID                # 主键类型：雪花算法
      logic-delete-field: deleted       # 逻辑删除字段名
      logic-delete-value: 1             # 逻辑删除值
      logic-not-delete-value: 0         # 逻辑未删除值
```

### Galaxy Boot配置

```yaml
galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 动态调整连接池大小
    dynamic-refresh-username-password: true # 动态刷新数据库密码
  metrics:
    datasource:
      prometheus:
        enabled: false
```

## API接口

### 博客管理接口

- `POST /api/blog/` - 自动创建博客
- `GET /api/blog/{id}` - 根据ID获取博客
- `GET /api/blog/all` - 获取所有博客
- `GET /api/blog/search/title?title=关键字` - 根据标题搜索
- `GET /api/blog/search/author?author=作者` - 根据作者搜索
- `PUT /api/blog/{id}` - 更新博客
- `DELETE /api/blog/{id}` - 删除博客（逻辑删除）
- `GET /api/blog/count/all` - 统计总数
- `GET /api/blog/count/author?author=作者` - 统计作者博客数
- `GET /api/blog/count/group-by-author` - 按作者分组统计

## 运行方式

1. 确保数据库连接配置正确
2. 启动应用：`mvn spring-boot:run`
3. 访问 http://localhost:8089
4. 使用 Postman 或其他工具测试 API

## 监控端点

- http://localhost:8089/actuator/prometheus - Prometheus监控指标
- http://localhost:8089/actuator/health - 健康检查

## 数据库表结构

```sql
CREATE TABLE `blog` (
  `id` bigint NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` text,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `author` varchar(100) DEFAULT NULL,
  `deleted` int DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```
