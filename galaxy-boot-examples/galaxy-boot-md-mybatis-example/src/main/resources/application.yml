server:
  port: 8082

spring:
  application:
    name: galaxy-boot-md-mybatis-example

  galaxy-datasource:
    product:
      primary: true
      type: mybatis  # 指定使用MyBatis
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.entity
        mapper: cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.mapper
      datasource:
        url: jdbc:h2:mem:productdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      # Hikari 连接池配置
      hikari:
        pool-name: ProductPool
        maximum-pool-size: 12
        minimum-idle: 3
        connection-timeout: 25s
        idle-timeout: 8m
        max-lifetime: 20m
        connection-test-query: SELECT 1
      mybatis:
        mapper-locations: classpath:mapper/product/*.xml
        type-aliases-package: cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.entity
        type-handlers-package: cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.typehandler
        executor-type: REUSE
        configuration:
          map-underscore-to-camel-case: true
          aggressive-lazy-loading: true
          cache-enabled: false
          safe-row-bounds-enabled: true
          safe-result-handler-enabled: false


galaxy:
  system:
    code: CNF

  metrics:
    datasource:
      prometheus:
        enabled: false

  log:
    default-category: APP_LOG

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: false

logging:
  level:
    cn.com.chinastock.cnf.mdatasource: DEBUG
    cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.mapper: DEBUG

apollo:
  bootstrap:
    enabled: false