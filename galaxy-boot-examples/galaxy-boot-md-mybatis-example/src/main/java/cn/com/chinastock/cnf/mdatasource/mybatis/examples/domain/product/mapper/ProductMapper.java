package cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.mapper;

import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface ProductMapper {
    
    Optional<Product> findById(@Param("id") Long id);
    
    Optional<Product> findByName(@Param("name") String name);
    
    List<Product> findAll();
    
    int save(Product product);
    
    int update(Product product);
    
    int deleteById(@Param("id") Long id);
    
    long count();
}
