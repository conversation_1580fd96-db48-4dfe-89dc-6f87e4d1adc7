package cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.mapper.ProductMapper;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
public class ProductService {

    private final ProductMapper productMapper;

    private final DataSource productDataSource;

    public ProductService(ProductMapper productMapper, @Qualifier("productDataSource") DataSource productDataSource) {
        this.productMapper = productMapper;
        this.productDataSource = productDataSource;
    }

    @PostConstruct
    public void initializeData() {
        initializeDatabase();
        initTestData();
    }

    public void initializeDatabase() {
        try {
            GalaxyLogger.info("----> Initializing Product database schema...");
            ScriptUtils.executeSqlScript(productDataSource.getConnection(),
                new ClassPathResource("sql/product-schema.sql"));
            GalaxyLogger.info("----> Product database schema initialized successfully.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Failed to initialize Product database schema: {}", e.getMessage());
            throw new RuntimeException("Failed to initialize Product database", e);
        }
    }

    @Transactional(transactionManager = "productTransactionManager")
    public Product createProduct(String name, BigDecimal price, String description) {
        Product product = new Product(name, price, description);
        productMapper.save(product);
        return product;
    }

    public Optional<Product> findById(Long id) {
        return productMapper.findById(id);
    }

    public Optional<Product> findByName(String name) {
        return productMapper.findByName(name);
    }

    public List<Product> findAll() {
        return productMapper.findAll();
    }

    @Transactional(transactionManager = "productTransactionManager")
    public int updateProduct(Product product) {
        return productMapper.update(product);
    }

    @Transactional(transactionManager = "productTransactionManager")
    public Optional<Product> updateProduct(Long id, String name, BigDecimal price, String description) {
        Optional<Product> existingProduct = findById(id);
        if (existingProduct.isPresent()) {
            Product product = existingProduct.get();
            if (name != null) {
                product.setName(name);
            }
            if (price != null) {
                product.setPrice(price);
            }
            if (description != null) {
                product.setDescription(description);
            }
            int updated = productMapper.update(product);
            if (updated > 0) {
                return Optional.of(product);
            }
        }
        return Optional.empty();
    }

    @Transactional(transactionManager = "productTransactionManager")
    public int deleteProduct(Long id) {
        return productMapper.deleteById(id);
    }

    public long countProducts() {
        return productMapper.count();
    }

    @Transactional(transactionManager = "productTransactionManager")
    public void initTestData() {
        GalaxyLogger.info("----> Writing to Product DataSource (MyBatis)...");
        try {
            Product product = new Product("Gaming Laptop", new BigDecimal("1299.99"), "High-end gaming laptop");
            productMapper.save(product);
            GalaxyLogger.info("----> Product write successful.");
        } catch (Exception e) {
            GalaxyLogger.error("----> Product write failed: {}", e.getMessage());
            throw new RuntimeException("Failed to write product data", e);
        }
    }

    @Transactional(transactionManager = "productTransactionManager")
    public void testTransactionRollback() {
        GalaxyLogger.info("----> Testing MyBatis transaction rollback...");
        Product productToRollback = new Product("Rollback Product", new BigDecimal("1.00"), "This should not exist");
        productMapper.save(productToRollback);
        GalaxyLogger.info("----> Product saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger MyBatis rollback");
    }

    public boolean verifyTransactionRollback() {
        Optional<Product> product = productMapper.findByName("Rollback Product");
        if (product.isPresent()) {
            GalaxyLogger.error("!!!!!! [FAILURE] MyBatis transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] MyBatis transaction rolled back successfully.");
            return true;
        }
    }

    public List<Product> verifyData() {
        List<Product> products = productMapper.findAll();
        products.forEach(p ->
                GalaxyLogger.info("[MyBatis Product] Found Product: ID={}, Name={}, Price={}",
                    p.getId(), p.getName(), p.getPrice())
        );
        return products;
    }
}
