package cn.com.chinastock.cnf.mdatasource.mybatis.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.dto.CreateProductRequest;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.dto.UpdateProductRequest;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.mybatis.examples.domain.product.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping
    public ResponseEntity<List<Product>> getAllProducts() {
        try {
            List<Product> products = productService.verifyData();
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to get all products", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Product> getProductById(@PathVariable Long id) {
        try {
            Optional<Product> product = productService.findById(id);
            return product.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find product by ID: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<Product> findProductByName(@RequestParam("name") String name) {
        try {
            Optional<Product> product = productService.findByName(name);
            return product.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            GalaxyLogger.error("Failed to find product by name: {}", name, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping
    public ResponseEntity<Product> createProduct(@RequestBody CreateProductRequest request) {
        try {
            Product product = productService.createProduct(request.getName(), request.getPrice(), request.getDescription());
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            GalaxyLogger.error("Failed to create product", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Product> updateProduct(@PathVariable Long id, @RequestBody UpdateProductRequest request) {
        try {
            Optional<Product> updatedProduct = productService.updateProduct(id, request.getName(), request.getPrice(), request.getDescription());
            return updatedProduct.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            GalaxyLogger.error("Failed to update product: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProduct(@PathVariable Long id) {
        try {
            productService.deleteProduct(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            GalaxyLogger.error("Failed to delete product: {}", id, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/mybatis-transaction")
    public ResponseEntity<Map<String, Object>> testMybatisTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            productService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("MyBatis transaction failed as expected: {}", e.getMessage());
            boolean rollbackSuccess = productService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess ? "MyBatis transaction rollback successful" : "MyBatis transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }
}
