package cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.service;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.repository.UserRepository;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Transactional
public class UserService {

    private static final IGalaxyLogger LOGGER =
            GalaxyLoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @PostConstruct
    public void init() {
        LOGGER.info("----> Initializing User Service (JPA)...");

        final int age = 28;
        User user = new User("john_doe", "<EMAIL>", age);
        User savedUser = userRepository.save(user);

        LOGGER.info("----> Sample user created: {}", savedUser);
    }

    public User createUser(final String username, final String email,
            final Integer age) {
        LOGGER.info("----> Creating user: username={}, email={}, age={}",
                username, email, age);
        
        User user = new User(username, email, age);
        User savedUser = userRepository.save(user);
        
        LOGGER.info("----> User created successfully: {}", savedUser);
        return savedUser;
    }

    @Transactional(transactionManager = "userTransactionManager")
    public List<User> getAllUsers() {
        LOGGER.info("----> Getting all users...");
        List<User> users = userRepository.findAll();
        LOGGER.info("----> Found {} users", users.size());
        return users;
    }

    @Transactional(transactionManager = "userTransactionManager")
    public User getUserById(Long id) {
        LOGGER.info("----> Getting user by ID: {}", id);
        return userRepository.findById(id).orElse(null);
    }

    @Transactional(transactionManager = "userTransactionManager")
    public List<User> findByUsername(String username) {
        LOGGER.info("----> Finding users by username: {}", username);
        return userRepository.findByUsername(username);
    }

    public User updateUser(Long id, String username, String email, Integer age) {
        LOGGER.info("----> Updating user by ID: {}", id);

        return userRepository.findById(id)
                .map(user -> {
                    if (username != null) {
                        user.setUsername(username);
                    }
                    if (email != null) {
                        user.setEmail(email);
                    }
                    if (age != null) {
                        user.setAge(age);
                    }
                    User updatedUser = userRepository.save(user);
                    LOGGER.info("----> User updated successfully: {}", updatedUser);
                    return updatedUser;
                })
                .orElse(null);
    }

    public void deleteUser(Long id) {
        LOGGER.info("----> Deleting user by ID: {}", id);
        userRepository.deleteById(id);
        LOGGER.info("----> User deleted successfully");
    }

    @Transactional(transactionManager = "userTransactionManager")
    public void testTransactionRollback() {
        GalaxyLogger.info("----> Testing JPA transaction rollback...");
        User userToRollback = new User("rollback_user", "<EMAIL>", 25);
        userRepository.save(userToRollback);
        GalaxyLogger.info("----> User saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger JPA rollback");
    }

    public boolean verifyTransactionRollback() {
        List<User> user = userRepository.findByUsername("rollback_user");
        if (!CollectionUtils.isEmpty(user)) {
            GalaxyLogger.error("!!!!!! [FAILURE] JPA transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            GalaxyLogger.info("====== [SUCCESS] JPA transaction rolled back successfully.");
            return true;
        }
    }
}
