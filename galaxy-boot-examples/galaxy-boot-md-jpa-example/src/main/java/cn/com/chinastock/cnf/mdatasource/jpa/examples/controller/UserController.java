package cn.com.chinastock.cnf.mdatasource.jpa.examples.controller;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.dto.CreateUserRequest;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.dto.UpdateUserRequest;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/users")
public final class UserController {

    @Autowired
    private UserService userService;

    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable final Long id) {
        User user = userService.getUserById(id);
        if (user != null) {
            return ResponseEntity.ok(user);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    public ResponseEntity<User> createUser(
            @RequestBody final CreateUserRequest request) {
        User user = userService.createUser(request.getUsername(),
                request.getEmail(), request.getAge());
        return ResponseEntity.ok(user);
    }

    @GetMapping("/search")
    public ResponseEntity<List<User>> findByUsername(
            @RequestParam("username") final String username) {
        List<User> users = userService.findByUsername(username);
        return ResponseEntity.ok(users);
    }


    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable final Long id,
            @RequestBody final UpdateUserRequest request) {
        User updatedUser = userService.updateUser(id, request.getUsername(),
                request.getEmail(), request.getAge());
        if (updatedUser != null) {
            return ResponseEntity.ok(updatedUser);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable final Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/jpa-transaction")
    public ResponseEntity<Map<String, Object>> testJpaTransaction() {
        Map<String, Object> result = new HashMap<>();
        try {
            userService.testTransactionRollback();
            result.put("success", false);
            result.put("message", "Transaction should have failed");
        } catch (Exception e) {
            GalaxyLogger.info("JPA transaction failed as expected: {}",
                    e.getMessage());
            boolean rollbackSuccess = userService.verifyTransactionRollback();
            result.put("success", rollbackSuccess);
            result.put("message", rollbackSuccess
                    ? "JPA transaction rollback successful"
                    : "JPA transaction rollback failed");
        }
        return ResponseEntity.ok(result);
    }
}
