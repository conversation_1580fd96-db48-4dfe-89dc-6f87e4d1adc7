package cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.repository;

import cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    List<User> findByUsername(String username);

}
