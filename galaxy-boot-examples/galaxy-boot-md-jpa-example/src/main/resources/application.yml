server:
  port: 8081

spring:
  application:
    name: galaxy-boot-md-jpa-example

  galaxy-datasource:
    user:
      primary: true
      type: jpa  # 指定使用JPA
      open-in-view: false # 禁用 OSIV
      # 我们自定义模块所需的注册信息
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.entity
        repository: cn.com.chinastock.cnf.mdatasource.jpa.examples.domain.user.repository
      # 标准的 Spring Boot DataSource 配置
      datasource:
        url: jdbc:h2:mem:userdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      # Hikari 连接池配置
      hikari:
        pool-name: UserPool
        maximum-pool-size: 8
        minimum-idle: 2
        connection-timeout: 20s
        idle-timeout: 5m
        max-lifetime: 15m
        leak-detection-threshold: 60s
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: create-drop
galaxy:
  system:
    code: CNF

  metrics:
    datasource:
      prometheus:
        enabled: false

  log:
    default-category: APP_LOG

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: false

logging:
  level:
    cn.com.chinastock.cnf.mdatasource: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.orm.jdbc.bind: TRACE

apollo:
  bootstrap:
    enabled: false